/* eslint-disable prefer-arrow/prefer-arrow-functions */
/* eslint-disable max-lines */
import { NotAcceptableException, NotFoundException } from '@nestjs/common';
import cloneDeep from 'lodash.clonedeep';
import moment from 'moment/moment';
import { DataSource, In, Repository, SelectQueryBuilder } from 'typeorm';
import {
  PreauthDateFilterType,
  PreauthorizationFilterInput,
} from '../inputs/pre-authorisation-filter.input';
import { PreauthorisationModel } from '../models/preauthorisation.model';
import {
  PreauthorisationResponse,
  PreauthorizationSummary,
} from '../responses/pre-authorisation.response';
import { queryDSWithSlave } from '@clinify/database';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { UserType } from '@clinify/shared/enums/users';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import {
  validateHmoRecordArchiver,
  validateRecordRemover,
} from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { takePaginatedResponses } from '@clinify/utils/pagination';

export interface IPreauthorizationRepository
  extends Repository<PreauthorisationModel> {
  this: Repository<PreauthorisationModel>;

  baseQuery(
    query: SelectQueryBuilder<PreauthorisationModel>,
    options: PreauthorizationFilterInput,
    keywordConditions: string,
    mutator?: ProfileModel,
  ): Promise<PreauthorisationResponse>;

  getPreauthorization(
    this: IPreauthorizationRepository,
    mutator: ProfileModel,
    id: string,
  ): Promise<PreauthorisationModel>;

  findByProfile(
    profileId: string,
    options: Partial<PreauthorizationFilterInput>,
    mutator: ProfileModel,
  ): Promise<PreauthorisationResponse>;

  findByHospital(
    hospitalId: string,
    options: Partial<PreauthorizationFilterInput>,
    mutator: ProfileModel,
  ): Promise<PreauthorisationResponse>;

  deletePreauthorizations(
    profile: ProfileModel,
    ids: string[],
  ): Promise<PreauthorisationModel[]>;

  archivePreauthorizations(
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<PreauthorisationModel[]>;

  getPreauthorizationSummary(
    dataSource: DataSource,
    mutator: ProfileModel,
    options: Partial<PreauthorizationFilterInput>,
    profileId?: string,
    providerId?: string,
  ): Promise<PreauthorizationSummary>;

  getProvidersWithRequestedPreauthorizations(
    dataSource: DataSource,
    mutator: ProfileModel,
    filterInput: PreauthorizationFilterInput,
  ): Promise<HospitalModel[]>;
}

export const CustomPreauthorizationRepoMethods: Pick<
  IPreauthorizationRepository,
  | 'baseQuery'
  | 'getPreauthorization'
  | 'findByProfile'
  | 'findByHospital'
  | 'deletePreauthorizations'
  | 'archivePreauthorizations'
  | 'getPreauthorizationSummary'
  | 'getProvidersWithRequestedPreauthorizations'
> = {
  async baseQuery(
    this: IPreauthorizationRepository,
    query: SelectQueryBuilder<PreauthorisationModel>,
    options: PreauthorizationFilterInput,
    keywordConditions: string,
    mutator?: ProfileModel,
  ): Promise<PreauthorisationResponse> {
    const {
      skip = 0,
      take = 10,
      dateRange,
      keyword,
      creator,
      status,
      hospitalId,
      hmo: providerId,
      providerType,
      filterDateField,
      showCompleted,
      showNotCompleted,
      showSubmitted,
      showNotSubmitted,
      timeSortOrder,
    } = options;
    const columnForDateFilter = determineDateField(filterDateField);
    if (creator)
      query = query
        .withDeleted()
        .innerJoinAndSelect('preauthorizations.createdBy', 'createdBy')
        .andWhere(
          creator === RecordCreator.SELF
            ? 'createdBy.type = :patient'
            : 'createdBy.type != :patient',
          { patient: UserType.Patient },
        );
    if (!mutator.hmoId) {
      if (hospitalId) {
        query = query.andWhere('preauthorizations.hospital_id = :hospitalId', {
          hospitalId,
        });
      }

      if (providerId) {
        query = query.andWhere('provider.id = :providerId', {
          providerId,
        });
      }
    }

    if (providerType) {
      query = query.andWhere('hospital.plan ILIKE :providerType', {
        providerType: `${providerType}%`,
      });
    }

    if (dateRange?.from) {
      query = query.andWhere(
        `(preauthorizations.${columnForDateFilter} >= :from)`,
        {
          from: dateRange.from,
        },
      );
    }

    if (dateRange?.to) {
      query = query.andWhere(
        `(preauthorizations.${columnForDateFilter} < :to)`,
        {
          to: dateRange.to,
        },
      );
    }

    if (status) {
      query = query.andWhere('utilizations.status = :status', { status });
    }

    if (keyword)
      query = query.andWhere(keywordConditions, { keyword: `%${keyword}%` });

    // Add completion status filtering
    if (showCompleted) {
      query = query.andWhere(
        `NOT EXISTS (
          SELECT 1 FROM pre_auth_utilisations u
          WHERE u.pre_auth = preauthorizations.id
          AND NOT EXISTS (
            SELECT 1 FROM jsonb_array_elements(u.utilisation_status) AS status_item
          )
        )
        AND NOT (
          preauthorizations.updated_date::timestamptz > ALL (
            SELECT COALESCE(
              (status_item->>'updatedDate')::timestamptz,
              (status_item->>'createdDate')::timestamptz
            ) + INTERVAL '1 second'
            FROM pre_auth_utilisations u2
            CROSS JOIN jsonb_array_elements(u2.utilisation_status) AS status_item
            WHERE u2.pre_auth = preauthorizations.id
            AND u2.utilisation_status IS NOT NULL
            AND jsonb_array_length(u2.utilisation_status) > 0
          )
        )`,
      );
    }
    if (showNotCompleted) {
      query = query.andWhere(
        `(
          EXISTS (
            SELECT 1 FROM pre_auth_utilisations u
            WHERE u.pre_auth = preauthorizations.id
            AND NOT EXISTS (
              SELECT 1 FROM jsonb_array_elements(u.utilisation_status) AS status_item
            )
          )
          OR
          preauthorizations.updated_date::timestamptz > ALL (
            SELECT COALESCE(
              (status_item->>'updatedDate')::timestamptz,
              (status_item->>'createdDate')::timestamptz
            ) + INTERVAL '1 second'
            FROM pre_auth_utilisations u2
            CROSS JOIN jsonb_array_elements(u2.utilisation_status) AS status_item
            WHERE u2.pre_auth = preauthorizations.id
            AND u2.utilisation_status IS NOT NULL
            AND jsonb_array_length(u2.utilisation_status) > 0
          )
        )`,
      );
    }
    if (showSubmitted) {
      // eslint-disable-next-line @typescript-eslint/quotes
      query = query.andWhere(`(preauthorizations.claimStatus = 'Submitted')`);
    }
    if (showNotSubmitted) {
      // eslint-disable-next-line @typescript-eslint/quotes
      query = query.andWhere(`(preauthorizations.claimStatus != 'Submitted')`);
    }

    if (timeSortOrder) {
      query = query
        .addSelect('preauthorizations.updated_date::date', 'updated_date_only')
        .addSelect('preauthorizations.updated_date::time', 'updated_time_only')
        .orderBy('updated_date_only', 'DESC')
        .addOrderBy('updated_time_only', timeSortOrder);
    } else {
      query = query.orderBy('preauthorizations.updatedDate', 'DESC');
    }

    query = query.skip(skip).take(take);

    const response = await query.getManyAndCount();

    return new PreauthorisationResponse(
      ...takePaginatedResponses(response, take),
    );
  },
  async getPreauthorization(
    _mutator: ProfileModel,
    id: string,
  ): Promise<PreauthorisationModel> {
    const preAuthorization = await this.findOneOrFail({
      where: {
        id,
      },
      relations: ['utilizations', 'profile', 'profile.details'],
      order: {
        utilizations: {
          type: 'ASC',
        },
      },
      withDeleted: true,
    }).catch(() => {
      throw new NotFoundException('Preauthorization Not Found');
    });
    return preAuthorization;
  },
  async findByProfile(
    this: IPreauthorizationRepository,
    profileId: string,
    options: Partial<PreauthorizationFilterInput>,
    mutator: ProfileModel,
  ): Promise<PreauthorisationResponse> {
    const query = this.createQueryBuilder('preauthorizations')
      .leftJoinAndSelect('preauthorizations.utilizations', 'utilizations')
      .leftJoinAndSelect('preauthorizations.provider', 'provider')
      .leftJoinAndSelect('preauthorizations.hospital', 'hospital')
      .where('preauthorizations.profileId = :profileId', { profileId })
      .andWhere('preauthorizations.archived = :archived', {
        archived: !!options.archive,
      });
    if (mutator.hmoId) {
      query.andWhere('(provider.id = :hmoId)', { hmoId: mutator.hmoId });
    }

    const keywordConditions = `
      (preauthorizations.code ILIKE :keyword OR
      preauthorizations.requested_by ILIKE :keyword OR
      preauthorizations.priority ILIKE :keyword OR
      preauthorizations.service_type ILIKE :keyword OR
      preauthorizations.service_name ILIKE :keyword OR
      utilizations.category ILIKE :keyword OR
      utilizations.pa_code ILIKE :keyword OR
      preauthorizations.diagnosis::text ILIKE :keyword OR
      provider.name ILIKE :keyword OR
      hospital.name ILIKE :keyword OR
      utilizations.type ILIKE :keyword OR
      preauthorizations.visitId ILIKE :keyword OR
      preauthorizations.claimId ILIKE :keyword OR
      preauthorizations.batchNumber ILIKE :keyword)
    `;

    return this.baseQuery(query, options, keywordConditions, mutator);
  },

  async findByHospital(
    this: IPreauthorizationRepository,
    hospitalId: string,
    options: Partial<PreauthorizationFilterInput>,
    mutator: ProfileModel,
  ): Promise<PreauthorisationResponse> {
    const query = this.createQueryBuilder('preauthorizations')
      .leftJoinAndSelect('preauthorizations.profile', 'profile')
      .leftJoinAndSelect('preauthorizations.utilizations', 'utilizations')
      .leftJoinAndSelect('preauthorizations.provider', 'provider')
      .leftJoinAndSelect('preauthorizations.hospital', 'hospital')
      .where('preauthorizations.archived = :archived', {
        archived: !!options.archive,
      });
    if (mutator.hmoId) {
      query.andWhere('provider.id = :hmoId', { hmoId: mutator.hmoId });
      if (options.hospitalId && !options.providerInsight) {
        query.andWhere('(preauthorizations.hospital_id = :hospitalId)', {
          hospitalId: options.hospitalId,
        });
      }
      if (options.providerInsight) {
        query.andWhere('(preauthorizations.hospital_id = :hospitalId)', {
          hospitalId,
        });
      }
    } else if (mutator.isPartnerProfile && mutator.partnerId) {
      query.andWhere('(hospital.partnerId = :partnerId)', {
        partnerId: mutator.partnerId,
      });
      if (options.hospitalId && !options.providerInsight) {
        query.andWhere('(preauthorizations.hospital_id = :hospitalId)', {
          hospitalId: options.hospitalId,
        });
      }
      if (options.providerInsight) {
        query.andWhere('(preauthorizations.hospital_id = :hospitalId)', {
          hospitalId,
        });
      }
    } else {
      query.andWhere('preauthorizations.hospital_id = :hospitalId', {
        hospitalId,
      });
    }
    const keywordConditions = `
      (preauthorizations.code ILIKE :keyword OR
      preauthorizations.requested_by ILIKE :keyword OR
      preauthorizations.priority ILIKE :keyword OR
      preauthorizations.service_type ILIKE :keyword OR
      preauthorizations.service_name ILIKE :keyword OR
      hospital.name ILIKE :keyword OR
      utilizations.category ILIKE :keyword OR
      utilizations.pa_code ILIKE :keyword OR
      preauthorizations.diagnosis::text ILIKE :keyword OR
      provider.name ILIKE :keyword OR
      profile.full_name ILIKE :keyword OR
      profile.clinify_id ILIKE :keyword OR
      utilizations.type ILIKE :keyword OR
      preauthorizations.visitId ILIKE :keyword OR
      preauthorizations.claimId ILIKE :keyword OR
      preauthorizations.enrolleeNumber ILIKE :keyword OR
      preauthorizations.batchNumber ILIKE :keyword)
    `;

    const result = await this.baseQuery(
      query,
      options,
      keywordConditions,
      mutator,
    );
    return result;
  },

  async deletePreauthorizations(
    this: IPreauthorizationRepository,
    profile: ProfileModel,
    ids: string[],
  ): Promise<PreauthorisationModel[]> {
    throw new NotAcceptableException('Record Cannot Be Deleted');
    const preAuthorizations = await this.find({ where: { id: In(ids) } });
    const validResources = validateRecordRemover(profile, preAuthorizations);
    if (validResources.length) await this.remove(cloneDeep(validResources));
    return validResources;
  },

  async archivePreauthorizations(
    this: IPreauthorizationRepository,
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<PreauthorisationModel[]> {
    const items = await this.find({
      relations: ['utilizations'],
      where: { id: In(ids) },
    });
    const validResources = validateHmoRecordArchiver(
      profile,
      items,
    ) as PreauthorisationModel[];
    const validIds = validResources.map((v) => v.id);
    if (!validIds.length) return [];
    await this.createQueryBuilder('pre_auths')
      .update(PreauthorisationModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();

    return validResources.map((v) => ({ ...v, archived: archive }));
  },
  async getPreauthorizationSummary(
    dataSource: DataSource,
    mutator: ProfileModel,
    options: Partial<PreauthorizationFilterInput>,
    profileId?: string,
    providerId?: string,
  ): Promise<PreauthorizationSummary> {
    const {
      status,
      archive = false,
      keyword,
      dateRange,
      providerInsight,
      hospitalId,
      hmo,
      providerType,
      filterDateField,
    } = options;
    const columnForDateFilter = determineDateField(filterDateField);

    let statusWhereCondition = '';
    if (status) {
      statusWhereCondition = `AND utilizations.status ILIKE '${status}'`;
    }

    const result = await queryDSWithSlave(
      dataSource,
      `
      SELECT
        COUNT(distinct preauthorizations.id) AS "totalPreauthorizations",
        COUNT(distinct preauthorizations.id) FILTER(WHERE utilizations.status ILIKE 'Approved') AS "totalApprovedApprovedPreauthorizations",
        COUNT(distinct preauthorizations.id) FILTER(WHERE utilizations.status ILIKE 'Rejected') AS "totalRejectedPreauthorizations",
        COUNT(distinct preauthorizations.id) FILTER(WHERE utilizations.status ILIKE 'Pending') AS "totalPendingPreauthorizations",
        SUM(utilizations.quantity::float * utilizations.price::float) AS "totalPreauthorizationsAmount",
        SUM(utilizations.quantity::float * utilizations.price::float) 
            FILTER(WHERE utilizations.status = 'Approved') AS "totalApprovedApprovedPreauthorizationsAmount",
        SUM(utilizations.quantity::float * utilizations.price::float) 
            FILTER(WHERE utilizations.status = 'Rejected') AS "totalRejectedPreauthorizationsAmount",
        SUM(utilizations.quantity::float * utilizations.price::float) 
            FILTER(WHERE utilizations.status = 'Pending') AS "totalPendingPreauthorizationsAmount"
        FROM pre_authorizations preauthorizations
        LEFT JOIN profiles ON preauthorizations.profile_id = profiles.id
        LEFT JOIN hmo_providers ON preauthorizations.provider_id = hmo_providers.id
        INNER JOIN pre_auth_utilisations utilizations ON preauthorizations.id = utilizations.pre_auth
        LEFT JOIN hospitals ON preauthorizations.hospital_id = hospitals.id
        WHERE preauthorizations.archived = '${archive ? 'yes' : 'no'}'
        ${
          providerInsight && mutator.hmoId
            ? `AND preauthorizations.provider_id = '${
                mutator.hmoId
              }' AND preauthorizations.status NOT IN ('draft', 'deleted') ${
                !!hospitalId
                  ? `AND preauthorizations.hospital_id = '${hospitalId}'`
                  : ''
              }`
            : ''
        }
        ${
          mutator.hmoId && !providerInsight
            ? `AND preauthorizations.provider_id = '${mutator.hmoId}'`
            : ''
        }
        ${
          hmo && !mutator.hmoId
            ? `AND preauthorizations.provider_id = '${hmo}'`
            : ''
        }
        ${
          mutator.isPartnerProfile && mutator.partnerId
            ? `AND hospitals.partner_id = '${mutator.partnerId}'`
            : ''
        }
        ${
          mutator.hospitalId && !mutator.hmoId && !mutator.isPartnerProfile
            ? `AND preauthorizations.hospital_id = '${mutator.hospitalId}'`
            : ''
        }
        ${
          providerId
            ? `AND preauthorizations.provider_id = '${providerId}'`
            : ''
        }
        ${
          hospitalId && !providerInsight
            ? `AND preauthorizations.hospital_id = '${hospitalId}'`
            : ''
        }
        ${profileId ? `AND preauthorizations.profile_id = '${profileId}'` : ''}
        ${!!providerType ? `AND hospitals.plan ILIKE '${providerType}%'` : ''}
        ${
          dateRange?.from
            ? `AND preauthorizations.${columnForDateFilter} >= '${moment(
                dateRange.from,
              ).startOf('day')}'`
            : ''
        }
        ${
          dateRange?.to
            ? `AND preauthorizations.${columnForDateFilter} < '${moment(
                dateRange.to,
              ).endOf('day')}'`
            : ''
        }
        ${statusWhereCondition}
        ${
          keyword
            ? `AND (
                preauthorizations.code ILIKE '%${keyword}%' OR
                preauthorizations.requested_by ILIKE '%${keyword}%' OR
                preauthorizations.priority ILIKE '%${keyword}%' OR
                preauthorizations.service_type ILIKE '%${keyword}%' OR
                preauthorizations.service_name ILIKE '%${keyword}%' OR
                utilizations.category ILIKE '%${keyword}%' OR
                utilizations.pa_code ILIKE '%${keyword}%' OR
                preauthorizations.diagnosis::text ILIKE '%${keyword}%' OR
                hmo_providers.name ILIKE '%${keyword}%' OR
                profiles.full_name ILIKE '%${keyword}%' OR
                profiles.clinify_id ILIKE '%${keyword}%' OR
                utilizations.type ILIKE '%${keyword}%' OR
                preauthorizations.visitId ILIKE '%${keyword}%' OR
                preauthorizations.claimId ILIKE '%${keyword}%' OR
                preauthorizations.enrolleeNumber ILIKE '%${keyword}%' OR
                preauthorizations.batchNumber ILIKE '%${keyword}%'
              )`
            : ''
        }
      `,
    );

    return result[0];
  },

  async getProvidersWithRequestedPreauthorizations(
    dataSource: DataSource,
    mutator: ProfileModel,
    filterInput: PreauthorizationFilterInput,
  ): Promise<HospitalModel[]> {
    const { dateRange, archive, filterDateField } = filterInput;
    const columnForDateFilter = determineDateField(filterDateField);
    if (!mutator.hmoId) return [];
    return queryDSWithSlave(
      dataSource,
      `SELECT DISTINCT hospitals.id,
                hospitals.name,
                hospitals.address,
                hospitals.support_mail as "supportMail",
                hospitals.support_phone_number as "hospitalSupportPhoneNumber"
        FROM pre_authorizations
        INNER JOIN hospitals ON pre_authorizations.hospital_id = hospitals.id
                                    AND pre_authorizations.provider_id = '${
                                      mutator.hmoId
                                    }'
        WHERE pre_authorizations.archived = '${archive ? 'yes' : 'no'}'
        ${
          dateRange?.from
            ? `AND pre_authorizations.${columnForDateFilter} >= '${moment(
                dateRange.from,
              ).startOf('day')}'`
            : ''
        }
        ${
          dateRange?.to
            ? `AND pre_authorizations.${columnForDateFilter} <= '${moment(
                dateRange.to,
              ).endOf('day')}'`
            : ''
        }
        ${
          filterInput.hospitalId
            ? `AND pre_authorizations.hospital_id = '${filterInput.hospitalId}'`
            : ''
        }`,
    );
  },
};

function determineDateField(
  t: PreauthDateFilterType,
): 'request_date_time' | 'created_date' {
  switch (t) {
    case PreauthDateFilterType.RequestDate:
      return 'request_date_time';
    case PreauthDateFilterType.CreatedDate:
      return 'created_date';
    default:
      return 'request_date_time';
  }
}
