import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import groupBy from 'lodash.groupby';
import { DataSource, EntityManager, In, IsNull, Not } from 'typeorm';
import { AgreedTariffInput } from '../inputs/agreed-tariff.input';
import { PreauthorizationFilterInput } from '../inputs/pre-authorisation-filter.input';
import { PreauthorisationInput } from '../inputs/preauthorisation.input';
import { PreauthorisationStatus } from '../interface/preauthorizations.interface';
import { PreauthorisationModel } from '../models/preauthorisation.model';
import { PreAuthUtilisationsModel } from '../models/utilisations.model';
import { IPreauthorizationRepository } from '../repositories/pre-authorization.repositories';
import { AgreedTariffResponse } from '../responses/agreed-tariff.response';
import {
  PreauthorisationResponse,
  PreauthorizationSummary,
} from '../responses/pre-authorisation.response';
import { queryDSWithSlave } from '@clinify/database';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { IHmoClaimRepository } from '@clinify/hmo-claims/repositories/hmo-claim.repository';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import { BenefitCategory } from '@clinify/hmo-providers/inputs/hmo-plan.input';
import { IHmoProviderMovePreauthorizationToClaimResponse } from '@clinify/hmo-providers/interface/hmo-providers.interface';
import { HmoProviderModel } from '@clinify/hmo-providers/models/hmo-provider.model';
import { HmoProviderService } from '@clinify/hmo-providers/services/hmo-provider.service';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import { AiProducer } from '@clinify/integrations/ai/producers/ai.producer';
import { FlagDto } from '@clinify/pre-authorisations/inputs/flag.dto';
import { HmoClaimStatus } from '@clinify/shared/enums/hmo-claims';
import { UserType } from '@clinify/shared/enums/users';
import { ClaimStatus } from '@clinify/shared/hmo-providers/modules/leadway/leadway.interface';
import { validateMovePreAuthToClaims } from '@clinify/shared/validators/validate-record-mutation.validator';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const { HMOPreauthorizationUpdated } = SubscriptionTypes;

@Injectable()
export class PreauthorisationService {
  constructor(
    @InjectRepository(PreauthorisationModel)
    private repo: IPreauthorizationRepository,
    @InjectRepository(HmoClaimModel)
    private hmoClaimRepo: IHmoClaimRepository,
    @Inject(forwardRef(() => HmoProviderService))
    private hmoProviderService: HmoProviderService,
    private readonly manager: EntityManager,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => AiProducer))
    private readonly aiProducer: AiProducer,
    @Inject(PUB_SUB) private pubSub: RedisPubSub,
  ) {}

  async getHmoProviderRoles(hmoId: string) {
    const hmoProviderRoles = await queryDSWithSlave(
      this.dataSource,
      `
        SELECT type
        FROM profiles
        WHERE hmo_id = $1
          AND profiles.deleted_date IS NULL
        GROUP BY type
      `,
      [hmoId],
    );
    return hmoProviderRoles.map((v) => v.type);
  }
  async addPreauthorization(
    mutator: ProfileModel,
    input: PreauthorisationInput,
    origin?: string,
  ): Promise<PreauthorisationModel> {
    const provider = await this.hmoProviderService.getHmoProviderModule(
      input.providerId,
      input.facilityId || mutator.hospitalId,
      mutator.clinifyId,
    );
    let pre: PreauthorisationModel;
    if (provider.isClinify) {
      pre = await this.hmoProviderService.requestPreAuthorization(
        mutator,
        input,
      );
      const groupUtilizations = groupBy(
        pre.utilizations,
        (util) => util.paymentModel,
      );
      const nonCapitatedUtilizations =
        groupUtilizations[BenefitCategory.FeeForService] || [];
      const capitatedUtilizations =
        groupUtilizations[BenefitCategory.Capitated] || [];
      if (capitatedUtilizations.length) {
        await this.hmoProviderService.submitCapitationClaim(
          mutator,
          {
            clinifyId: input.clinifyId,
            enrolleeId: input.enrolleeId,
            claimDate: input.requestDateTime,
            treatmentStartDate: input.treatmentStartDate,
            treatmentEndDate: input.treatmentEndDate,
            facilityName: input.facilityName,
            facilityAddress: input.facilityAddress,
            status: ClaimStatus.Submitted,
            rank: input.rank,
            department: input.department,
            specialty: input.specialty,
            presentingComplain: input.presentingComplain,
            referredBy: input.referredBy,
            referralCode: input.referralCode,
            referredFrom: input.referredFrom,
            referredTo: input.referredTo,
            providerId: input.providerId,
            serviceType: input.serviceType,
            serviceTypeCode: input.serviceTypeCode,
            serviceName: input.serviceName,
            priority: input.priority,
            diagnosis: input.diagnosis,
            utilizations: [],
            patientType: input.patientType,
            paymentType: input.paymentType,
            additionalNote: input.additionalNote,
            documentUrl: input.documentUrl,
            memberUniqueId: input.memberUniqueId,
            facilityId: input.facilityId,
            enrolleeEmail: input.enrolleeEmail,
            enrolleePhoneNumber: input.enrolleePhoneNumber,
            isExternalPlanType: input.isExternalPlanType,
            externalPlanTypeId: input.externalPlanTypeId,
          },
          capitatedUtilizations,
          origin,
        );
      }
      if (!nonCapitatedUtilizations.length) {
        throw new UnprocessableEntityException(
          '[IGNORE]Capitation Utilizations Submitted',
        );
      }
      pre.utilizations = nonCapitatedUtilizations;
    } else {
      pre = await provider.requestPreAuthorization({
        ...input,
        doctorCode: mutator.fullName,
        staffEmail: mutator.user.email,
      });
    }
    const profile = await this.manager.findOne(ProfileModel, {
      where: {
        clinifyId: input?.clinifyId,
      },
    });
    if (!profile) {
      throw new NotFoundException('Patient Not Found');
    }
    const preAuth = new PreauthorisationModel({
      ...pre,
      profileId: profile.id,
      providerId: input.providerId,
      hospitalId: input.facilityId || mutator.hospitalId,
      creatorName: mutator.fullName,
      creatorId: mutator.id,
      createdBy: mutator,
      profile,
      enrolleeNumber: input.enrolleeId,
    });
    const savedPreAuth = await this.repo.save(preAuth);
    if (provider.isClinify) {
      this.aiProducer.addPreAuthApprovalQueue({ id: savedPreAuth.id });
    }
    return savedPreAuth;
  }
  async updatePreauthorization(
    mutator: ProfileModel,
    preAuthId: string,
    input: PreauthorisationInput,
  ): Promise<PreauthorisationModel> {
    const provider = await this.hmoProviderService.getHmoProviderModule(
      input.providerId,
      input.facilityId || mutator.hospitalId,
      mutator.clinifyId,
    );
    const savedPreAuth = await this.manager.transaction(async (manager) => {
      if (!provider.isClinify) return;
      const savedPreAuth = await this.hmoProviderService.updatePreAuthorization(
        mutator,
        preAuthId,
        input,
        manager,
      );
      return savedPreAuth;
    });
    if (provider.isClinify) {
      this.aiProducer.addPreAuthApprovalQueue({ id: savedPreAuth.id });
    }
    return savedPreAuth;
  }

  async getPreauthorization(
    mutator: ProfileModel,
    id: string,
  ): Promise<PreauthorisationModel> {
    const preAuthorisation = await this.repo.getPreauthorization(mutator, id);
    return preAuthorisation;
  }

  async synchronizePreauthorizations(
    mutator: ProfileModel,
    ids: string[],
  ): Promise<PreauthorisationModel[]> {
    const preAuths = await this.repo.find({
      where: { id: In(ids) },
      relations: ['utilizations'],
    });
    if (!preAuths.length)
      throw new NotFoundException('Preauthorization Not Found');
    const provider = await this.hmoProviderService.getHmoProviderModule(
      preAuths[0].providerId,
      mutator.hospitalId,
      mutator.clinifyId,
    );
    if (provider.isClinify) {
      const preAuths = await this.manager.find(PreauthorisationModel, {
        where: { id: In(ids) },
        relations: ['utilizations'],
      });
      return preAuths;
    }
    return Promise.all(
      preAuths.map(async (preAuth) => {
        const utilizations = await provider.synchronizePreauthorizations(
          preAuth.visitId,
        );
        preAuth.utilizations = preAuth.utilizations.map((util) => {
          const selectedUtil = utilizations.find((singleUtil) =>
            provider.isPrognosis
              ? singleUtil.visitDetailsId === util.visitDetailsId
              : singleUtil.utilizationId === util.utilizationId,
          );
          if (selectedUtil) {
            return new PreAuthUtilisationsModel({
              ...util,
              ...selectedUtil,
              paCode: selectedUtil.paCode || util.paCode,
            });
          }
          return util;
        });
        return this.repo.save(preAuth);
      }),
    );
  }
  async movePreauthorizationToClaims(
    mutator: ProfileModel,
    ids: string[],
  ): Promise<[PreauthorisationModel[], HmoClaimModel[]]> {
    const preAuths = await this.repo.find({
      where: { id: In(ids) },
      relations: ['utilizations', 'profile', 'provider'],
    });
    const isSameProvider = preAuths.every(
      (preAuth) => preAuth.providerId === preAuths[0].providerId,
    );
    if (!isSameProvider)
      throw new UnprocessableEntityException(
        'Preauthorizations Not From The Same Provider',
      );

    let hospitalId = mutator.hospitalId;
    if (mutator.hmoId === preAuths[0].providerId) {
      hospitalId = preAuths[0].hospitalId;
    }
    const provider = await this.hmoProviderService.getHmoProviderModule(
      preAuths[0].providerId,
      hospitalId,
      mutator.clinifyId,
    );
    const createdClaims: HmoClaimModel[] = [];
    const updatedPreauthorizations: PreauthorisationModel[] = [];

    for (const item of preAuths) {
      if (!item.utilizations.length) {
        continue;
      }
      // eslint-disable-next-line @typescript-eslint/unbound-method
      const h = this.hmoProviderService.movePreAuthUtilizationToClaim;
      type ClinifyMovePreAuthToClaimsResponse = Awaited<ReturnType<typeof h>>;
      let res:
        | string
        | IHmoProviderMovePreauthorizationToClaimResponse
        | ClinifyMovePreAuthToClaimsResponse;
      const hmoProfile = await this.manager
        .findOneOrFail(HmoProfileModel, {
          where: {
            providerId: item.providerId,
            profileId: item.profileId,
          },
          select: [
            'memberUniqueId',
            'memberPlanId',
            'memberNumber',
            'providerId',
          ],
        })
        .catch(() => {
          throw new NotFoundException('HMO Profile Not Found');
        });
      if (provider.isPrognosis) {
        res = await provider.movePreauthorizationToClaims(
          item.visitId,
          mutator.user.email,
        );
      } else if (provider.isClinify) {
        const resp = (res =
          await this.hmoProviderService.movePreAuthUtilizationToClaim(
            mutator,
            hmoProfile,
            { ...item },
          ));
        if (!resp.utilizations?.length) {
          updatedPreauthorizations.push({
            ...item,
            claimStatus: resp.claimStatus,
            claimId: res.claimId,
            batchNumber: res.batchNumber,
          });
          continue;
        }
      } else if (!validateMovePreAuthToClaims(mutator, item)) {
        continue;
      } else {
        const key = `${item.utilizations.length}-${
          hmoProfile.memberNumber
        }-${item.utilizations.reduce(
          (acc, curr) => acc + Number(curr.price) * Number(curr.quantity),
          0,
        )}`;
        res = await provider.movePreauthorizationToClaims(item.visitId, key);
      }
      updatedPreauthorizations.push({
        ...item,
        ...(provider.isClinify
          ? { claimStatus: (res as any)?.claimStatus }
          : {
              claimStatus:
                'utilizations' in res &&
                res.utilizations.length < item.utilizations.length
                  ? 'Partially Submitted'
                  : 'Submitted',
            }),
        claimId: res.claimId,
        batchNumber: res.batchNumber,
      });
      const claimConfirmExist = await this.manager.findOne(ProfileModel, {
        select: ['id'],
        where: {
          type: UserType.ClaimConfirmation,
          hmoId: item.providerId,
        },
      });

      createdClaims.push(
        new HmoClaimModel({
          visitId: res.visitDetailsId,
          visitDetailsId: res.visitDetailsId,
          claimIdentity: res.claimIdentity,
          claimId: res.claimId,
          batchNumber: res.batchNumber,
          createdBy: mutator,
          documentUrl: item.documentUrl,
          isExternalPlanType: item.isExternalPlanType,
          externalPlanTypeId: item.externalPlanTypeId,
          utilizations:
            'utilizations' in res
              ? res.utilizations
              : item.utilizations.map(
                  (util) =>
                    new PreAuthUtilisationsModel({
                      paCode: util.paCode,
                      status: util.status,
                      visitDetailsId: util.visitDetailsId,
                      utilizationCode: util.utilizationCode,
                      utilizationId: util.utilizationId,
                      type: util.type,
                      quantity: util.quantity,
                      price: util.price,
                      category: util.category,
                      hmoProviderId: util.hmoProviderId,
                      profileId: util.profileId,
                      frequency: util.frequency,
                      duration: util.duration,
                      paUtilProcessed: !!util.utilisationStatus?.length,
                      paymentModel: util.paymentModel,
                    }),
                ),
          claimDate: item.requestDateTime || new Date(),
          department: item.department,
          diagnosis: item.diagnosis,
          status: claimConfirmExist
            ? HmoClaimStatus.Unconfirmed
            : ClaimStatus.Submitted,
          facilityName: item.facilityName,
          facilityAddress: item.facilityAddress,
          serviceName: item.serviceName,
          serviceType: item.serviceType,
          presentingComplain: item.presentingComplain,
          additionalNote: item.additionalNote,
          priority: item.priority,
          specialty: item.specialty,
          rank: item.rank,
          serviceTypeCode: item.serviceTypeCode,
          profile: item.profile,
          totalTariffFee:
            'utilizations' in res
              ? res.totalTariffFee.toString()
              : item.utilizations
                  .reduce(
                    (acc, curr) =>
                      acc + Number(curr.price) * Number(curr.quantity),
                    0,
                  )
                  .toString(),
          treatmentStartDate: item.treatmentStartDate,
          treatmentEndDate: item.treatmentEndDate,
          creatorName: mutator.fullName,
          hospitalId,
          providerId: item.providerId,
          memberPlanID: hmoProfile?.memberUniqueId,
          memberUniqueID: hmoProfile?.memberPlanId,
          enrolleeNumber: hmoProfile?.memberNumber,
          submitDateTime: new Date(),
          submittedBy: mutator.fullName,
          enrolleeEmail: item.enrolleeEmail,
          enrolleePhoneNumber: item.enrolleePhoneNumber,
          referredBy: item.referredBy,
          referralCode: item.referralCode,
          referredFrom: item.referredFrom,
          referredTo: item.referredTo,
        }),
      );
    }

    const claim = await this.manager
      .withRepository(this.hmoClaimRepo)
      .save(createdClaims);
    if (claim.length) {
      for (const item of claim) {
        if (item.status.toLowerCase() !== 'draft') {
          this.aiProducer.addClaimApprovalQueue({ id: item.id });
        }
      }
    }

    const preAuth = await this.repo.save(updatedPreauthorizations);
    return [preAuth, claim];
  }
  async getAgreedTariff(
    mutator: ProfileModel,
    input: AgreedTariffInput,
  ): Promise<AgreedTariffResponse> {
    const provider = await this.hmoProviderService.getHmoProviderModule(
      input.providerId,
      input.facilityId || mutator.hospitalId,
      mutator.clinifyId,
    );
    if (provider.isClinify) {
      const res = await this.hmoProviderService.getAgreedTariff(mutator, {
        utilizationCode: input.id,
        utilizationCategory: input.utilizationId,
        enrolleeId: input.enrolleeId,
        providerId: input.providerId,
        visitTypeId: input.visitTypeId,
        facilityId: input.facilityId,
        externalPlanId: input.externalPlanId,
      });
      return {
        success: true,
        amount: res.cost,
        position: input.position,
        label: res.label,
        utilisationCategory: res.utilisationCategory,
        utilisationCategoryId: res.utilisationCategoryId,
        capitated: res.capitated,
      };
    }
    if (provider.isPrognosis) {
      const res: any = await provider.getAgreedTariff({
        procedureCode: input.id,
        utilizationId: input.utilizationId,
      });
      return {
        success: true,
        amount: res.cost,
        position: input.position,
        label: res.label,
      };
    }

    const amount: any = await provider.getAgreedTariff({
      procedureCode: input.id,
      memberNumber: input.enrolleeId,
      treatmentDate: input.treatmentDate,
    });
    return {
      success: true,
      amount,
      position: input.position,
    };
  }

  async findByHospital(
    hospitalId: string,
    options: PreauthorizationFilterInput,
    mutator: ProfileModel,
  ): Promise<PreauthorisationResponse> {
    return this.repo.findByHospital(hospitalId, options, mutator);
  }

  async findByProfile(
    profileId: string,
    options: PreauthorizationFilterInput,
    mutator: ProfileModel,
  ): Promise<PreauthorisationResponse> {
    return this.repo.findByProfile(profileId, options, mutator);
  }
  async deletePreauthorizations(
    profile: ProfileModel,
    ids: string[],
  ): Promise<PreauthorisationModel[]> {
    const preAuthorisation = await this.repo.deletePreauthorizations(
      profile,
      ids,
    );
    return preAuthorisation;
  }
  async archivePreauthorizations(
    profile: ProfileModel,
    ids: string[],
    archive: boolean,
  ): Promise<PreauthorisationModel[]> {
    const preAuthorisation = await this.repo.archivePreauthorizations(
      profile,
      ids,
      archive,
    );
    return preAuthorisation;
  }

  async getHmoProvider(providerId: string) {
    return this.manager.findOneOrFail(HmoProviderModel, {
      where: { id: providerId },
    });
  }

  async getUtilization(
    _mutator: ProfileModel,
    paCode: string,
    utilizationId: string,
    utilType?: string,
  ): Promise<PreAuthUtilisationsModel> {
    let additionalWhere = {};

    if (utilType === 'PREAUTH') {
      additionalWhere = { preAuthorizationId: Not(IsNull()) };
    } else if (utilType === 'CLAIM') {
      additionalWhere = { hmoClaimId: Not(IsNull()) };
    }

    const res = await this.manager.findOne(PreAuthUtilisationsModel, {
      where: { paCode, utilizationId, ...additionalWhere },
    });

    return res;
  }

  async updatePreauthUtilizationQuantity(
    mutator: ProfileModel,
    id: string,
    quantity: number,
  ): Promise<PreAuthUtilisationsModel> {
    const updateEntity = {
      id,
      quantity: `${quantity}`,
      lastModifierId: mutator.id,
      lastModifierName: mutator.fullName,
    };

    const res = await this.manager.update(
      PreAuthUtilisationsModel,
      { id, hmoProviderId: mutator.hmoId },
      updateEntity,
    );
    if (!res.affected) {
      throw new UnprocessableEntityException('Utilization Not Found');
    }

    return new PreAuthUtilisationsModel(updateEntity);
  }

  private setNewFlags(
    existingFlags: FlagDto[] | null,
    flag: string,
    unset: boolean,
    mutator: ProfileModel,
  ): FlagDto[] | null {
    const newFlags = unset
      ? (existingFlags || []).filter((f) => f.flag !== flag)
      : (existingFlags || []).concat({
          flag,
          flaggedById: mutator.id,
          flaggedByFullname: mutator.fullName,
          flaggedByRole: mutator.type,
          flagDateAndTime: new Date(),
        });

    return newFlags.length > 0 ? newFlags : null;
  }

  async flagPreauthorization(
    mutator: ProfileModel,
    id: string,
    flag: string,
    unset: boolean,
  ): Promise<PreauthorisationModel> {
    const preauth = await this.repo.findOne({
      where: { id, providerId: mutator.hmoId },
    });
    if (!preauth) {
      throw new NotFoundException('Preauthorization Not Found');
    }

    return this.repo.save({
      ...preauth,
      lastModifierId: mutator.id,
      lastModifierName: mutator.fullName,
      flags: this.setNewFlags(preauth.flags, flag, unset, mutator),
      updatedDate: preauth.updatedDate, // TODO: verify this
    });
  }

  async flagUtilizations(
    mutator: ProfileModel,
    utilizationIds: string[],
    flag: string,
    unset: boolean,
  ): Promise<PreAuthUtilisationsModel[]> {
    let utils = await this.manager.find(PreAuthUtilisationsModel, {
      where: { id: In(utilizationIds), hmoProviderId: mutator.hmoId },
    });
    if (!utils.length) {
      throw new NotFoundException('Utilizations Not Found');
    }

    utils = utils.map((util) => {
      return new PreAuthUtilisationsModel({
        ...util,
        flags: this.setNewFlags(util.flags, flag, unset, mutator),
      });
    });

    return this.manager.save(utils);
  }

  getProvidersWithRequestedPreauthorizations(
    mutator: ProfileModel,
    filterInput: PreauthorizationFilterInput,
  ): Promise<HospitalModel[]> {
    return this.repo.getProvidersWithRequestedPreauthorizations(
      this.dataSource,
      mutator,
      filterInput,
    );
  }

  getPreauthorizationSummary(
    mutator: ProfileModel,
    filterInput: PreauthorizationFilterInput,
    profileId?: string,
    providerId?: string,
  ): Promise<PreauthorizationSummary> {
    return this.repo.getPreauthorizationSummary(
      this.dataSource,
      mutator,
      filterInput,
      profileId,
      providerId,
    );
  }

  async handleMedicationPreauthorizationStatusUpdate(
    id: string,
    medicationName: string,
    hmoClaimId: string,
    manager?: EntityManager,
  ) {
    manager = manager || this.manager;
    const preauthRepo = manager.withRepository(this.repo);
    const preauthorization = await preauthRepo.findOne({
      where: { id },
      relations: [
        'utilizations',
        'createdBy',
        'profile',
        'provider',
        'updatedBy',
      ],
    });
    preauthorization.utilizations = await Promise.all(
      preauthorization.utilizations.map(async (util) => {
        if (util.type === medicationName) {
          util.medicationClaimId = hmoClaimId;
          await manager.update(
            PreAuthUtilisationsModel,
            { id: util.id },
            { medicationClaimId: hmoClaimId },
          );
        }
        return util;
      }),
    );
    if (
      preauthorization.utilizations.every(
        ({ medicationClaimId }) => !!medicationClaimId,
      )
    ) {
      preauthorization.status = PreauthorisationStatus.Submitted;
      preauthorization.claimStatus = 'Submitted';
    } else if (
      preauthorization.utilizations.some(
        ({ medicationClaimId }) => !!medicationClaimId,
      )
    ) {
      preauthorization.claimStatus = 'Partially Submitted';
    }
    await this.pubSub.publish(HMOPreauthorizationUpdated, {
      [HMOPreauthorizationUpdated]: preauthorization,
    });
    await preauthRepo.update(
      {
        id,
      },
      {
        claimStatus: preauthorization.claimStatus,
        status: preauthorization.status,
        updatedDate: () => 'updated_date',
      },
    );
  }
}
