/* eslint-disable max-lines */
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import groupBy from 'lodash.groupby';
import moment from 'moment';
import { nanoid } from 'nanoid';
import { DataSource, EntityManager, In, IsNull, Not } from 'typeorm';
import { v4 as generateUUID, validate } from 'uuid';
import util from 'util';
import { HospitalModel } from '../../hospitals/models/hospital.model';
import curlHttpService from '../../shared/helper/curl-http-service';
import {
  IHmoEnrolleeBenefit,
  IHmoProvider,
} from '../interface/hmo-providers.interface';
import { HmoHospitalModel } from '../models/hmo-hospital.model';
import { HmoPlanBenefitModel } from '../models/hmo-plan-benefit.model';
import { HmoProviderModel } from '../models/hmo-provider.model';
import { HmoVisitTypeModel } from '../models/hmo-visit-type.model';
import { HmoProviderRepository } from '../repositories/hmo-provider.repository';
import {
  VirtualAccountTransactionType,
  VirtualAccountType,
} from '@clinify/banks/enum/virtual-account.enum';
import { TransferFundModel } from '@clinify/banks/models/transfer-fund.model';
import { BankService } from '@clinify/banks/services/bank.service';
import { BusinessRuleActionInput } from '@clinify/cms/inputs/business-rule.input';
import { BusinessRuleService } from '@clinify/cms/services/business-rule.service';
import {
  customDSSerializeInTransaction,
  queryDSWithSlave,
} from '@clinify/database';
import { FacilityPreferenceModel } from '@clinify/facility-preferences/models/facility-preference.model';
import { HmoClaimFilterInput } from '@clinify/hmo-claims/inputs/hmo-claim.filter.input';
import { NewHmoClaimInput } from '@clinify/hmo-claims/inputs/hmo-claims.input';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { IHmoClaimRepository } from '@clinify/hmo-claims/repositories/hmo-claim.repository';
import { HmoProfileModel } from '@clinify/hmo-profiles/models/hmo-profile.model';
import { LASHMA_NAME } from '@clinify/hmo-providers/constants/constants';
import { BenefitCustomPrice } from '@clinify/hmo-providers/dtos/BenefitCustomPrice';
import {
  HmoPlanInput,
  HmoPlanStatus,
  NewHmoPlanInput,
  UpdatePlanTypeBenefit,
} from '@clinify/hmo-providers/inputs/hmo-plan.input';
import {
  CheckinInput,
  CoverageLookupOptions,
  EmailTransactionReceiptInput,
  GetAgreedTariffInput,
  HmoFilterOptions,
  HmoPlanBeneficiariesFilterOptions,
  HmoPlanFilterOptions,
  LeadwayProviderLoginInput,
  PaCodeStatus,
  UpdateBenefitCustomPriceInput,
  UpdatePreAuthUtilizationStatusInput,
  UpdateUtilizationsStatusInput,
} from '@clinify/hmo-providers/inputs/hmo-provider.input';
import { HmoPlanTypeModel } from '@clinify/hmo-providers/models/hmo-plan-type.model';
import bulkPrintCapitationPaymentAdvice from '@clinify/hmo-providers/pdfs/capitation-payout-advice-doc-def';
import { CapitatedEnrolleesListData } from '@clinify/hmo-providers/responses/CapitatedEnrolleesData';
import {
  GenerateHospitalPaymentDocResponse,
  HmoBeneficiariesResponse,
  HmoBenefitResponse,
  HmoCapitatedPaymentSummary,
  HmoHospitalPaymentsResponse,
  HmoHospitalResponse,
  HmoPlanBeneficiariesResponse,
  HmoPlanTypesResponse,
  HmoProviderResponse,
  HmoProviderStaffsResponse,
  HmoUtilizationCategoriesResponse,
  HmoUtilizationTypesResponse,
  HmoVisitationTypesResponse,
  TransactionReceiptSentResponse,
  UtilizationTypeObject,
} from '@clinify/hmo-providers/responses/hmo.response';
import { TwilioWhatsAppService } from '@clinify/integrations/twilio-whatsapp/services/twilio-whatsapp.service';
import { TEMPLATE_SIDS } from '@clinify/integrations/twilio-whatsapp/template_sids';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { MedicationDetailsModel } from '@clinify/medications/models/medication_details.model';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { PreauthorisationReferralInput } from '@clinify/pre-authorisations-referral/inputs/preauthorisation-referral.input';
import { FlagDto } from '@clinify/pre-authorisations/inputs/flag.dto';
import { PreauthUtilisationInput } from '@clinify/pre-authorisations/inputs/preauth-utilisation.input';
import {
  PreauthorisationInput,
  PreauthorisationUpdateInput,
} from '@clinify/pre-authorisations/inputs/preauthorisation.input';
import { PreauthorisationModel } from '@clinify/pre-authorisations/models/preauthorisation.model';
import { PreAuthUtilisationsModel } from '@clinify/pre-authorisations/models/utilisations.model';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { BillableRecords } from '@clinify/shared/enums/billable-records';
import { EventType } from '@clinify/shared/enums/events';
import { MedicationOptionType } from '@clinify/shared/enums/medication';
import {
  DashboardIcon,
  NotificationTag,
} from '@clinify/shared/enums/notifications';
import { AppServices } from '@clinify/shared/enums/services';
import { UserType } from '@clinify/shared/enums/users';
import {
  getThemeFromUrl,
  ITemplateTheme,
} from '@clinify/shared/generators/theme-generator';
import {
  formatLog,
  hmoRoleUtilizationValidation,
} from '@clinify/shared/helper';
import { formatMoney, toNaira } from '@clinify/shared/helper/billing';
import {
  hmoAgencyUserProcessOrder,
  getOrderedItemsAndNextItem,
} from '@clinify/shared/helper/get-ordered-list-and-next-item';
import hmoProviders from '@clinify/shared/hmo-providers';
import { LeadwaySDK } from '@clinify/shared/hmo-providers/modules/leadway/sdk';
import { PrognosisHmoProvider } from '@clinify/shared/hmo-providers/modules/prognosis';
import { MAILER } from '@clinify/shared/mailer/constants';
import { IMessage } from '@clinify/shared/mailer/mailer.interface';
import { MailerService } from '@clinify/shared/mailer/mailer.service';
import { sendClaimsReportTemplate } from '@clinify/shared/mailer/mailer.template';
import { mailDocService as MailDocService } from '@clinify/shared/services/mail-doc.service';
import { DateRangeInput } from '@clinify/shared/validators/date-range.input';
import { CoverageDetailsInput } from '@clinify/users/inputs/profile-details.input';
import { CoverageInformationModel } from '@clinify/users/models/coverage-information.model';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { ProfilesResponse } from '@clinify/users/responses/profile.responses';
import { convertToNumber } from '@clinify/utils/dynamic-pdf/generate-enrollee-claim-details-doc';
import { takePaginatedResponses } from '@clinify/utils/pagination';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  MedicationAdded,
  MedicationUpdated,
  MedicationRemoved,
  MedicationEvent,
  PrescriptionEvent,
  HMOPreauthorizationUpdated,
  HMOClaimAdded,
} = SubscriptionTypes;
@Injectable()
export class HmoProviderService {
  constructor(
    private readonly repository: HmoProviderRepository,
    private entityManager: EntityManager,
    @Inject(MAILER) private mailerService: MailerService,
    private readonly logger: Logger,
    private readonly bankService: BankService,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => MailDocService))
    private mailDocService: MailDocService,
    @InjectRepository(HmoClaimModel)
    private readonly hmoClaimRepo: IHmoClaimRepository,
    private readonly businessRuleService: BusinessRuleService,
    @Inject(PUB_SUB) private pubSub: RedisPubSub,
    private notificationService: NotificationsService,
    private readonly twilioWhatsappService: TwilioWhatsAppService,
  ) {}

  async getHmoProviderById(hmoId: string) {
    return this.entityManager.findOne(HmoProviderModel, {
      where: { id: hmoId },
    });
  }
  async getProviderByCodeOrId(providerId: string) {
    const isProviderCode = !validate(providerId);

    const hmoProvider = await this.entityManager.findOne(HmoProviderModel, {
      where: {
        ...(isProviderCode ? { providerCode: providerId } : { id: providerId }),
      },
      select: ['id', 'name', 'providerCode'],
    });
    if (!hmoProvider) {
      throw new BadRequestException('HMO Not Found');
    }
    return hmoProvider;
  }
  async getHmoProviderRoles(hmoId: string): Promise<UserType[]> {
    const hmoProviderRoles = await queryDSWithSlave(
      this.dataSource,
      `
        SELECT type
        FROM profiles
        WHERE hmo_id = $1
          AND profiles.deleted_date IS NULL
        GROUP BY type
      `,
      [hmoId],
    );
    return hmoProviderRoles.map((v) => v.type);
  }
  async fetchHmoProviders(
    filterOptions: HmoFilterOptions,
  ): Promise<HmoProviderResponse> {
    const providers = await this.repository.fetchHmos(filterOptions);
    return providers;
  }
  /**
   * The `providerId` is our internal generated ID for a specific HMO provider e.g. Leadway HMO.
   *
   * The `hospitalid` is our internal generated ID for a specific hospital through which we will use along with
   * `providerId` to retrieve the hospital HMO assigned ID.
   */
  async getHmoProviderModule(
    providerId: string,
    hospitalId: string,
    clinifyId: string,
    isAgency?: boolean,
  ) {
    const info = await this.entityManager.findOne(HmoProviderModel, {
      where: {
        id: providerId,
      },
      select: ['id', 'name', 'providerCode'],
    });

    if (!info) {
      throw new BadRequestException('HMO Not Found');
    }

    const HmoProvider = hmoProviders[+info.providerCode];
    if (!HmoProvider) {
      throw new BadRequestException(`HMO ${info.name} Is Not Supported`);
    }
    if (isAgency) {
      return new HmoProvider(
        info.name,
        providerId,
        '',
        '',
        curlHttpService,
      ) as IHmoProvider;
    }

    const hmoHospital = await this.getHospitalHmoProvider(
      providerId,
      hospitalId,
    );
    if (!hmoHospital) {
      throw new BadRequestException(`HMO ${info.name} Is Not Enabled`);
    }

    const instance = new HmoProvider(
      info.name,
      providerId,
      hmoHospital.hmoProviderId,
      +info.providerCode === 1 ? clinifyId : hmoHospital.hmoProviderUniqueId,
      curlHttpService,
    );

    return instance as IHmoProvider;
  }

  getHmoProviderModuleByHmoHospital(
    clinifyId: string,
    hmoHospital: HmoHospitalModel,
  ) {
    const HmoProvider = hmoProviders[hmoHospital.provider.providerCode];
    if (!HmoProvider) {
      throw new BadRequestException(
        `HMO ${hmoHospital.provider.name} Is Not Supported`,
      );
    }
    const instance = new HmoProvider(
      hmoHospital.provider.name,
      hmoHospital.providerId,
      hmoHospital.hmoProviderId,
      +hmoHospital.provider.providerCode === 1
        ? clinifyId
        : hmoHospital.hmoProviderUniqueId,
      curlHttpService,
    );

    return instance as IHmoProvider;
  }

  async getHmoProviderByCode(providerCode: string) {
    const hmoProvider = await this.entityManager.findOne(HmoProviderModel, {
      where: {
        providerCode,
      },
    });
    if (!hmoProvider) {
      throw new BadRequestException('HMO Not Found');
    }
    return hmoProvider;
  }

  async enableWhatsAppNotifications(
    providerCode: string,
    enableWhatsAppNotifications: boolean,
  ): Promise<HmoProviderModel> {
    const hmoProvider = await this.entityManager.findOne(HmoProviderModel, {
      where: {
        providerCode,
      },
    });
    if (!hmoProvider) {
      throw new BadRequestException('HMO Provider Not Found');
    }

    return this.entityManager.save(HmoProviderModel, {
      ...hmoProvider,
      enableWhatsAppNotifications,
    });
  }

  async getHmoProvider(
    providerId: string,
    hospitalId: string,
    clinifyId: string,
  ) {
    const info = await this.entityManager.findOne(HmoProviderModel, {
      where: {
        id: providerId,
      },
      select: ['id', 'name', 'providerCode'],
    });

    if (!info) {
      throw new BadRequestException('HMO Not Found');
    }
    const HmoProvider = hmoProviders[info.providerCode];
    if (!HmoProvider) {
      throw new BadRequestException(`HMO ${info.name} Is Not Supported`);
    }

    const { hmoProviderId, hmoProviderUniqueId } =
      await this.getHospitalHmoProvider(providerId, hospitalId);
    const instance = new HmoProvider(
      info.name,
      providerId,
      hmoProviderId,
      +info.providerCode === 1 ? clinifyId : hmoProviderUniqueId,
      curlHttpService,
    );

    return instance as IHmoProvider;
  }

  async findByProfile(profile: ProfileModel): Promise<HmoProviderModel> {
    const hmoHospitals = await this.entityManager.find(HmoHospitalModel, {
      where: {
        hospitalId: profile.hospitalId,
      },
      select: ['providerId'],
    });
    const providers = await this.repository.findOne({
      where: {
        id: In(hmoHospitals.map((hmoHospital) => hmoHospital.providerId)),
      },
    });
    return providers;
  }
  async getHospitalHmoProvider(providerId: string, hospitalId: string) {
    const hmoHospital = await this.entityManager.findOne(HmoHospitalModel, {
      where: {
        hospitalId,
        providerId,
      },
      select: ['hmoProviderId', 'hmoProviderUniqueId'],
    });

    return hmoHospital;
  }
  async verifyEnrollee(
    input: CoverageLookupOptions,
    mutator: ProfileModel,
  ): Promise<ProfilesResponse> {
    const hmoProvider = await this.getProviderByCodeOrId(input.providerId);

    const provider = await this.getHmoProviderModule(
      hmoProvider.id,
      mutator.hospitalId,
      mutator.clinifyId,
      !!mutator.hmoId,
    );
    let enrollees: ProfileModel[] = [];

    if (provider.isClinify) {
      if (['107', '20'].includes(hmoProvider.providerCode)) {
        const hmoHospital = await this.getHospitalHmoProvider(
          hmoProvider.id,
          mutator.hospitalId,
        );
        const prognosisProvider = new PrognosisHmoProvider(
          LASHMA_NAME,
          mutator.hospitalId,
          mutator.hmoId ? '' : hmoHospital.hmoProviderId,
          mutator.hmoId ? '' : hmoHospital.hmoProviderUniqueId,
          curlHttpService,
        );
        enrollees = await prognosisProvider.getEnrollees(input.enrolleeId);
      }
    } else {
      enrollees = await provider.getEnrollees(input.enrolleeId);
    }
    const profiles = await Promise.all(
      enrollees.map(async (enrollee) => {
        if (!enrollee.hmoProfiles) {
          enrollee.hmoProfiles = [];
        }
        const matchingProfile = await this.entityManager.findOne(
          HmoProfileModel,
          {
            where: {
              memberNumber: enrollee.hmoProfiles[0].memberNumber,
              providerId: hmoProvider.id,
            },
            relations: ['profile', 'profile.user', 'profile.details'],
          },
        );
        const profile = {
          ...enrollee,
          hmoProfiles: enrollee.hmoProfiles.map((hmoProfile) => ({
            id: generateUUID(),
            ...hmoProfile,
            provider: hmoProvider,
          })),
          coverageDetails: enrollee.hmoProfiles.map(
            (hmoProfile) =>
              ({
                id: generateUUID(),
                coverageType: 'HMO',
                name: LASHMA_NAME,
                memberNumber: hmoProfile.memberNumber,
                memberPlan: hmoProfile.memberPlan,
                memberStatus: hmoProfile.memberStatus,
                memberDueDate: hmoProfile.memberDueDate,
                hmoProfile,
                provider: hmoProvider,
              } as CoverageDetailsInput),
          ),
          ...matchingProfile?.profile,

          hmos: {
            totalCount: enrollee.hmoProfiles.length,
            list: enrollee.hmoProfiles.map((hmoProfile) => ({
              id: generateUUID(),
              ...matchingProfile,
              ...hmoProfile,
              provider: hmoProvider,
            })),
          },
        };
        return profile;
      }),
    );
    return new ProfilesResponse(
      ...takePaginatedResponses([profiles, profiles.length], 10),
    );
  }

  async checkinEnrollee(
    input: CheckinInput,
    mutator: ProfileModel,
  ): Promise<ProfilesResponse> {
    const checkInsResponse = await Promise.all(
      input.enrollees.map(async ({ enrolleeId, profileId }) => {
        if (!enrolleeId && !profileId) {
          throw new BadRequestException('Enrollee ID Not Valid');
        }
        const hmoProfile = await this.entityManager.findOne(HmoProfileModel, {
          where: enrolleeId
            ? {
                memberNumber: enrolleeId,
              }
            : {
                profileId,
              },
          select: ['memberNumber', 'providerId', 'memberUniqueId'],
        });
        if (!hmoProfile?.providerId && !hmoProfile?.memberNumber) {
          throw new BadRequestException('Enrollee ID Not Valid');
        }
        const provider = await this.getHmoProviderModule(
          hmoProfile.providerId,
          mutator.hospitalId,
          mutator.clinifyId,
        );
        if (provider.isClinify) {
          const response = await this.repository.checkInEnrollee(
            mutator,
            hmoProfile.providerId,
            hmoProfile.memberNumber,
          );
          return {
            response,
            enrolleeId: hmoProfile.memberNumber,
            providerId: hmoProfile.providerId,
          };
        }
        if (provider.isPrognosis) {
          const response = await provider.checkInEnrollee({
            memberUniqueId: hmoProfile.memberUniqueId,
            enrolleeId: hmoProfile.memberNumber,
            visitType: 'Inpatient',
            staffEmail: mutator.user.email,
          });
          return {
            response,
            enrolleeId: hmoProfile.memberNumber,
            providerId: hmoProfile.providerId,
          };
        }
        const response = await provider.checkInEnrollee(enrolleeId);
        return {
          response,
          enrolleeId: hmoProfile.memberNumber,
          providerId: hmoProfile.providerId,
        };
      }),
    );

    const profiles = await Promise.all(
      checkInsResponse.map(async ({ response, enrolleeId, providerId }) => {
        return this.repository.updateLastCheckinInformation(
          enrolleeId,
          providerId,
          {
            providerId,
            checkInByProviderId: providerId,
            checkInByProfileId: mutator.id,
            checkInDate: new Date(),
            checkInByHospitalId: mutator.hospitalId,
            verificationCode: response.verificationCode,
            visitId: response.visitationId,
            checkedIn: true,
          },
        );
      }),
    );
    return new ProfilesResponse(
      ...takePaginatedResponses([profiles, profiles.length], 10),
    );
  }

  async extractCoverageInformation(
    input: { providerId: string; enrolleeId: string },
    mutator: ProfileModel,
  ): Promise<HmoProfileModel> {
    const hmoProvider = await this.getProviderByCodeOrId(input.providerId);
    let hospitalId = mutator.hospitalId;
    if (!hospitalId) {
      const profile = await this.entityManager.findOne(ProfileModel, {
        where: {
          id: mutator.id,
        },
        relations: ['registeredWith'],
      });
      hospitalId = profile.registeredWithId;
    }
    input.providerId = hmoProvider.id;
    const providerService = await this.getHmoProviderModule(
      input.providerId,
      hospitalId,
      mutator.clinifyId,
    );
    if (providerService.isClinify) {
      const profile = await this.entityManager.findOne(HmoProfileModel, {
        where: {
          memberNumber: input.enrolleeId,
          providerId: input.providerId,
        },
      });

      return {
        ...profile,
        provider: hmoProvider,
      };
    }
    const res = await providerService.getEnrolleeDetails(input.enrolleeId);
    const profile = await this.entityManager.findOne(HmoProfileModel, {
      where: {
        memberNumber: input.enrolleeId,
        providerId: input.providerId,
      },
      select: ['memberNumber'],
    });

    return {
      ...profile,
      ...res,
      provider: hmoProvider,
    };
  }

  async getEnrolleeBenefits(
    id: string,
    mutator: ProfileModel,
  ): Promise<HmoBenefitResponse[]> {
    try {
      const profiles = await this.entityManager.find(HmoProfileModel, {
        where: {
          profileId: id,
          memberNumber: Not(IsNull()),
        },
        select: ['provider', 'memberNumber', 'memberUniqueId', 'memberPlanId'],
        relations: ['provider'],
      });

      const values = await Promise.all(
        profiles.map(
          async ({
            memberNumber,
            provider: hmoProvider,
            memberPlanId,
            memberUniqueId,
          }) => {
            const provider = await this.getHmoProviderModule(
              hmoProvider.id,
              mutator.hospitalId,
              mutator.clinifyId,
              !!mutator.hmoId,
            );
            const input = memberUniqueId
              ? { memberUniqueId, memberPlanId }
              : memberNumber;
            let benefits: IHmoEnrolleeBenefit[];

            if (provider.isClinify) {
              benefits = await this.repository
                .getEnrolleeBenefits(this.dataSource, {
                  providerId: hmoProvider.id,
                  planTypeId: memberPlanId,
                  profileId: id,
                })
                .catch(() => []);
            } else {
              benefits = await provider
                .getEnrolleeBenefits(input)
                .catch(() => []);
            }

            return {
              success: true,
              errorMsg: '',
              provider: hmoProvider,
              benefits,
            };
          },
        ),
      );

      return values;
    } catch (error) {
      return [
        {
          success: false,
          errorMsg: error.message || 'Error in request',
          provider: null,
          benefits: [],
        },
      ];
    }
  }

  async getEnrolleeBeneficiaries(
    profileId: string,
    mutator: ProfileModel,
  ): Promise<HmoBeneficiariesResponse> {
    try {
      const profiles = await this.entityManager.find(HmoProfileModel, {
        where: {
          profileId,
          memberNumber: Not(IsNull()),
        },
        select: ['provider', 'memberNumber'],
        relations: ['provider'],
      });

      const values = await Promise.all(
        profiles.map(async ({ memberNumber, provider }) => {
          const hmoProviderModule = await this.getHmoProviderModule(
            provider.id,
            mutator.hospitalId,
            mutator.clinifyId,
            !!mutator.hmoId,
          );
          if (hmoProviderModule.isClinify) {
            return this.repository
              .getEnrolleeBeneficiaries(memberNumber, provider.id)
              .catch(() => []);
          }
          const beneficiaries = await hmoProviderModule
            .getEnrolleeBeneficiaries(memberNumber)
            .catch(() => []);
          return beneficiaries.map((beneficiary) => ({
            ...beneficiary,
            hmoProvider: provider,
          }));
        }),
      );

      return {
        success: true,
        errorMsg: '',
        beneficiaries: values.flat(),
      };
    } catch (error) {
      return {
        success: false,
        errorMsg: error.message || 'Error in request',
        beneficiaries: [],
      };
    }
  }
  async generateHospitalPaymentDocuments(
    providerId: string,
    mutator: ProfileModel,
  ): Promise<GenerateHospitalPaymentDocResponse> {
    const provider = await this.getHmoProviderModule(
      providerId,
      mutator.hospitalId,
      mutator.clinifyId,
    );
    try {
      const res = await provider.generateHospitalPaymentDocument();

      const msg: IMessage = {
        to: mutator.user.email,
        subject: 'Payment Advice',
        html: sendClaimsReportTemplate({
          pdfLink: res?.pdfLink,
          excelLink: res?.pdfLink,
          fullName: `${mutator.fullName}`.trim(),
        }),
      };

      await this.mailerService.sendMail(msg);

      return {
        success: true,
        errorMsg: '',
        documentUrl: res?.pdfLink,
        excelUrl: res?.excelLink,
      };
    } catch (e) {
      this.logger.warn(
        e.message,
        formatLog({
          service: AppServices.HmoProvider,
          action: 'HmoProviderService.generateLeadwayHospitalPaymentDocuments',
          traceMsg: util.format(e),
          actorClinifyId: mutator.clinifyId,
          hospital: mutator.hospitalId,
        }),
      );
      return {
        success: false,
        errorMsg: e.message || 'Error, could not get payment document',
        documentUrl: '',
        excelUrl: '',
      };
    }
  }

  async getHospitalHmoPayments(input: {
    clinifyId: string;
    providerId: string;
    hospitalId: string;
    filterOptions: DateRangeInput;
  }): Promise<HmoHospitalPaymentsResponse> {
    const provider = await this.getHmoProviderModule(
      input.providerId,
      input.hospitalId,
      input.clinifyId,
    );
    const list = await provider.getHospitalPayments({
      ...input.filterOptions,
      hospitalId: input.hospitalId,
    });
    return {
      list,
    };
  }

  async linkHospitalToHmoProvider(input: {
    hospitalId: string;
    providerId: string;
    hmoHospitalProviderId: string;
  }): Promise<HmoProviderModel> {
    const hospital = await this.entityManager.findOne(HospitalModel, {
      where: {
        id: input.hospitalId,
      },
      relations: ['wallet'],
    });
    if (!hospital) throw new Error('Hospital Not Found');
    const hmoProvider = await this.entityManager.findOne(HmoProviderModel, {
      where: {
        id: input.providerId,
      },
      select: ['id'],
    });
    if (!hmoProvider) throw new Error('HMO Not Found');

    const hmoHospitals = await this.entityManager.findOne(HmoHospitalModel, {
      where: {
        hospitalId: input.hospitalId,
        providerId: input.providerId,
      },
      select: ['id'],
    });
    if (hmoHospitals) throw new Error('HMO Already Linked To Facility');

    const newHmoHospital = new HmoHospitalModel({
      hospitalId: input.hospitalId,
      providerId: input.providerId,
      hmoProviderId: input.hmoHospitalProviderId,
    });
    await this.entityManager.save(newHmoHospital);
    if (hospital.wallet) {
      await this.bankService.generateVirtualAccountNumber(
        {
          virtualAccountType: VirtualAccountType.Permanent,
          wallet: hospital.wallet,
          vaTransactionType: VirtualAccountTransactionType.Hmo,
        },
        hospital.name,
      );
    }
    return hmoProvider;
  }

  private async getEnrolleeDetailsSlim(providerId: string, clinifyId: string) {
    const profile = await this.entityManager.query(
      `SELECT hmo_profiles.member_plan_id,hmo_profiles.member_unique_id FROM profiles
        LEFT JOIN hmo_profiles ON (profiles.id = hmo_profiles.profile_id AND provider_id = '${providerId}')
        WHERE clinify_id = '${clinifyId}'
        `,
    );
    if (!profile.length) {
      throw new BadRequestException('HMO Profile Not Found');
    }
    return {
      memberPlanId: profile[0].member_plan_id,
      memberUniqueId: profile[0].member_unique_id,
    };
  }
  async getUtilizationTypes(
    input: {
      providerId: string;
      utilizationId: string;
      clinifyId: string;
      visitationType?: string;
      facilityId?: string;
    },
    mutator: ProfileModel,
  ): Promise<HmoUtilizationTypesResponse> {
    const { providerId, clinifyId, utilizationId, visitationType, facilityId } =
      input;
    const provider = await this.getHmoProviderModule(
      input.providerId,
      facilityId || mutator.hospitalId,
      mutator.clinifyId,
    );
    if (provider.isClinify) {
      const list = await this.repository.getUtilisationTypes(
        input.providerId,
        input.visitationType,
        input.utilizationId,
      );
      return {
        list,
      };
    } else if (provider.isPrognosis) {
      const details = await this.getEnrolleeDetailsSlim(providerId, clinifyId);
      const list = await provider.getUtilizationTypes({
        memberPlanId: details.memberPlanId,
        memberUniqueId: details.memberUniqueId,
        visitationType,
        utilizationId,
      });
      return {
        list,
      };
    }
    const list = await provider.getUtilizationTypes(input.utilizationId);
    return {
      list,
    };
  }

  async getVisitationTypes(
    providerId: string,
    clinifyId: string,
    mutator: ProfileModel,
    hospitalId?: string,
    externalPlanTypeId?: string,
  ): Promise<HmoVisitationTypesResponse> {
    const provider = await this.getHmoProviderModule(
      providerId,
      hospitalId || mutator.hospitalId,
      mutator.clinifyId,
    );
    if (provider.isClinify) {
      const details = externalPlanTypeId
        ? { memberPlanId: externalPlanTypeId }
        : await this.getEnrolleeDetailsSlim(providerId, clinifyId);
      const list = await this.repository.getVisitationTypes(
        providerId,
        details.memberPlanId,
      );
      return {
        list,
      };
    } else if (provider.isPrognosis) {
      const details = await this.getEnrolleeDetailsSlim(providerId, clinifyId);
      const list = await provider.getVisitationTypes({
        memberPlanId: details.memberPlanId,
        memberUniqueId: details.memberUniqueId,
      });
      return {
        list,
      };
    }
    const profile = await this.entityManager.query(
      `SELECT hmo_profiles.member_number FROM profiles
        LEFT JOIN hmo_profiles ON profiles.id = hmo_profiles.profile_id
        WHERE clinify_id = '${clinifyId}' AND provider_id = '${providerId}'
        `,
    );

    if (!profile.length) {
      throw new BadRequestException('HMO Coverage Not Found');
    }

    const list = await provider.getVisitationTypes(profile[0].member_number);
    return {
      list,
    };
  }

  async getUtilizationCategories(
    input: {
      providerId: string;
      clinifyId: string;
      visitationType?: string;
      facilityId?: string;
    },
    mutator: ProfileModel,
  ): Promise<HmoUtilizationCategoriesResponse> {
    const { providerId, clinifyId, visitationType, facilityId } = input;
    const provider = await this.getHmoProviderModule(
      providerId,
      facilityId || mutator.hospitalId,
      mutator.clinifyId,
    );
    if (provider.isClinify) {
      const list = await this.repository.getUtilisationCategories(
        providerId,
        visitationType,
      );
      return {
        list,
      };
    } else if (provider.isPrognosis) {
      const profile = await this.entityManager.query(
        `SELECT hmo_profiles.member_plan_id,hmo_profiles.member_unique_id FROM profiles
        LEFT JOIN hmo_profiles ON (profiles.id = hmo_profiles.profile_id AND provider_id = '${providerId}')
        WHERE clinify_id = '${clinifyId}'
        `,
      );
      if (!profile.length) {
        throw new BadRequestException('HMO Profile Not Found');
      }
      const list = await provider.getUtilizationCategories({
        memberPlanId: profile[0].member_plan_id,
        memberUniqueId: profile[0].member_unique_id,
        visitationType,
      });
      return {
        list,
      };
    }
    const profile = await this.entityManager.query(
      `SELECT hmo_profiles.member_number FROM profiles
        LEFT JOIN hmo_profiles ON profiles.id = hmo_profiles.profile_id
        WHERE clinify_id = '${clinifyId}' AND provider_id = '${providerId}'
        `,
    );

    if (!profile?.length) {
      throw new BadRequestException('HMO Coverage Not Found');
    }
    const list = await provider.getUtilizationCategories(
      profile[0].member_number,
    );
    return {
      list,
    };
  }
  async getHmoProviderByFacilityId(
    facilityId: string,
    filterOptions: HmoFilterOptions,
  ): Promise<HmoProviderResponse> {
    const providers = await this.repository.getHmoProviderByFacilityId(
      facilityId,
      filterOptions,
    );
    return providers;
  }
  getRegisteredHmoHospitals(
    hmoId: string | null,
    filterOptions: HmoFilterOptions,
  ): Promise<HmoHospitalResponse> {
    if (!hmoId) {
      return Promise.resolve(new HmoHospitalResponse([], 0));
    }
    return this.repository.getRegisteredHmoHospitals(
      this.dataSource,
      hmoId,
      filterOptions,
    );
  }

  async leadwayProviderLogin(
    profile: ProfileModel,
    input: LeadwayProviderLoginInput,
  ) {
    const leadwaySdk = new LeadwaySDK(profile.clinifyId);
    const res = await leadwaySdk.providerLogin(input).catch((e) => {
      throw new BadRequestException(e.message);
    });
    await leadwaySdk.setAccessToken({
      token: res.accessToken,
      userId: res.user_id,
    });
    return true;
  }

  async checkLeadwayAccessToken(profile: ProfileModel) {
    const leadwaySdk = new LeadwaySDK(profile.clinifyId);
    return leadwaySdk.checkToken();
  }
  getHmoHospitalByProviderId(
    providerId: string,
    filterOptions: HmoFilterOptions,
  ): Promise<HmoHospitalResponse> {
    return this.repository.getHmoHospitalByProviderId(
      this.dataSource,
      providerId,
      filterOptions,
    );
  }
  async getProviderStaffs(
    profile: ProfileModel,
  ): Promise<HmoProviderStaffsResponse[]> {
    const hmoHospitals = await this.entityManager.find(HmoHospitalModel, {
      where: {
        hospitalId: profile.hospitalId,
      },
      relations: ['provider'],
    });
    const response: HmoProviderStaffsResponse[] = [];
    for (const hmoHospital of hmoHospitals) {
      try {
        const provider = this.getHmoProviderModuleByHmoHospital(
          profile.clinifyId,
          hmoHospital,
        );

        const staffs = await provider.getProviderStaffs(
          hmoHospital.hmoProviderUniqueId,
        );
        response.push({
          providerName: hmoHospital.provider.name,
          staffs,
        });
      } catch (error) {
        this.logger.error(
          error.message,
          formatLog({
            service: AppServices.HmoProvider,
            action: 'HmoProviderService.getProviderStaffs',
            traceMsg: util.format(error),
            actorClinifyId: profile.clinifyId,
            hospital: profile.hospitalId,
          }),
        );
      }
    }
    return response;
  }

  async createPlan(
    mutator: ProfileModel,
    providerId: string,
    input: NewHmoPlanInput,
  ): Promise<HmoPlanTypeModel> {
    const savedPlan = await customDSSerializeInTransaction(
      this.dataSource,
      (manager: EntityManager) =>
        manager
          .withRepository(this.repository)
          .createPlan(mutator, providerId, input),
    );
    return savedPlan;
  }

  async duplicatePlan(
    mutator: ProfileModel,
    id: string,
  ): Promise<HmoPlanTypeModel> {
    const planType = await this.repository.getPlanTypeById(id);
    if (!planType) {
      throw new NotFoundException('Plan Not Found');
    }
    const input: NewHmoPlanInput = {
      name: `${planType.name} - Copy`,
      planDateTime: planType.planDateTime,
      plannedBy: planType.plannedBy,
      premiumCountry: planType.premiumCountry,
      status: planType.status,
      premiumDetails: planType.premiumDetails,
      benefits: planType.benefits?.map((benefit) => {
        return {
          visitType: benefit.visitType.name,
          code: benefit.code,
          utilisationCategory: benefit.utilisationCategory,
          benefitLimit: benefit.limit,
          visitLimit: benefit.visitLimit,
          annualLimitPerPerson: benefit.annualLimitPerPerson,
          waitingPeriodDays: benefit.waitingPeriodDays,
          utilisationTypes: benefit.utilisationTypes?.map((utilizationType) => {
            return {
              id: nanoid(10),
              name: utilizationType.name,
              code: utilizationType.code,
              price: utilizationType.price,
              bandBPrice: utilizationType.bandBPrice,
              bandCPrice: utilizationType.bandCPrice,
              benefitCategory: utilizationType.benefitCategory,
              benefitCoverage: utilizationType.benefitCoverage,
              benefitLimit: utilizationType.benefitLimit,
              visitLimit: utilizationType.visitLimit,
              annualLimitPerPerson: utilizationType.annualLimitPerPerson,
              waitingPeriodDays: utilizationType.waitingPeriodDays,
              quantity: utilizationType.quantity,
            };
          }),
        };
      }),
    };

    return this.createPlan(mutator, planType.hmoProviderId, input);
  }

  async updatePlan(
    mutator: ProfileModel,
    input: HmoPlanInput,
  ): Promise<HmoPlanTypeModel> {
    const savedPlan = await customDSSerializeInTransaction(
      this.dataSource,
      (manager: EntityManager) =>
        manager.withRepository(this.repository).updatePlan(mutator, input),
    );
    return savedPlan;
  }

  async updateHmoPlanBenefitsInTransaction(
    manager: EntityManager,
    mutator: ProfileModel,
    providerId: string,
    planTypeId: string,
    input: UpdatePlanTypeBenefit[],
  ): Promise<HmoPlanBenefitModel[]> {
    const existingRecords = await manager.find(HmoPlanBenefitModel, {
      where: {
        planTypeId,
        id: In(input.filter(({ id }) => id).map(({ id }) => id)),
      },
      relations: {
        visitType: true,
      },
    });
    const visitTypeStore: Array<
      | {
          id: string;
          name: string;
        }
      | HmoVisitTypeModel
    > = [];

    const entities = await Promise.all(
      input.map(async (item) => {
        let existingRecord = existingRecords.find(({ id }) => id === item.id);

        if (!existingRecord && !item?.visitTypeId) {
          const _visitType = await manager.findOne(HmoVisitTypeModel, {
            where: { name: item.visitTypeName, planTypeId },
          });

          if (_visitType?.id) {
            item.visitTypeId = _visitType.id;
          }
        }

        if (!existingRecord && item?.utilisationCategory && item?.visitTypeId) {
          existingRecord = await manager.findOne(HmoPlanBenefitModel, {
            where: {
              planTypeId,
              utilisationCategory: item?.utilisationCategory,
              visitTypeId: item?.visitTypeId,
            },
            relations: { visitType: true },
          });

          if (existingRecord) {
            existingRecords.push(existingRecord);
          }
        }

        if (existingRecord) {
          if (existingRecord.visitType.name !== item.visitTypeName) {
            await manager.update(
              HmoVisitTypeModel,
              existingRecord.visitType.id,
              {
                name: item.visitTypeName,
                updatedBy: mutator,
                lastModifierName: mutator.fullName,
              },
            );
          }
          return {
            ...existingRecord,
            ...item,
            name: item.utilisationCategory,
            visitType: {
              ...existingRecord.visitType,
              name: item.visitTypeName,
            },
            ...(existingRecord
              ? {
                  updatedBy: mutator,
                  lastModifierName: mutator.fullName,
                }
              : {
                  createdBy: mutator,
                  creatorName: mutator.fullName,
                }),
          };
        }
        const determineVisitType = (name: string) => {
          const found = visitTypeStore.find(
            ({ name: visitTypeName }) => visitTypeName === name,
          );
          if (!found) {
            const len = visitTypeStore.push({
              id: generateUUID(),
              name,
            });
            return visitTypeStore[len - 1].id;
          }
        };
        let _visitType = existingRecords.find(
          ({ visitTypeId }) => visitTypeId === item.visitTypeId,
        )?.visitType;
        if (!_visitType && item.visitTypeId) {
          _visitType = await manager.findOne(HmoVisitTypeModel, {
            where: { id: item.visitTypeId, planTypeId },
          });
        }
        if (!_visitType) {
          const _vtId = determineVisitType(item.visitTypeName);
          if (_vtId) {
            _visitType = await manager.save(
              HmoVisitTypeModel,
              new HmoVisitTypeModel({
                id: _vtId,
                name: item.visitTypeName,
                planTypeId,
                createdBy: mutator,
                creatorName: mutator.fullName,
                hmoProviderId: providerId,
              }),
            );
            const storeIndex = visitTypeStore.findIndex(
              ({ id: visitTypeId }) => visitTypeId === _vtId,
            );
            visitTypeStore[storeIndex] = _visitType;
          }
        } else if (_visitType.name !== item.visitTypeName) {
          await manager.update(HmoVisitTypeModel, _visitType.id, {
            name: item.visitTypeName,
            updatedBy: mutator,
            lastModifierName: mutator.fullName,
          });
        }
        return {
          ...item,
          name: item.utilisationCategory,
          visitType:
            visitTypeStore.find(({ name }) => item.visitTypeName === name) ||
            _visitType,
          createdBy: mutator,
          creatorName: mutator.fullName,
          hmoProviderId: providerId,
          planTypeId,
        } as HmoPlanBenefitModel;
      }),
    );

    return manager.save(HmoPlanBenefitModel, entities);
  }

  async updateHmoPlanBenefits(
    mutator: ProfileModel,
    providerId: string,
    planTypeId: string,
    input: UpdatePlanTypeBenefit[],
  ): Promise<HmoPlanBenefitModel[]> {
    return customDSSerializeInTransaction(this.dataSource, (manager) =>
      this.updateHmoPlanBenefitsInTransaction(
        manager,
        mutator,
        providerId,
        planTypeId,
        input,
      ),
    );
  }

  async deleteHmoPlanBenefits(
    mutator: ProfileModel,
    planTypeId: string,
    benefitIds: string[],
    utilizationTypeIds?: string[],
  ): Promise<HmoPlanTypeModel> {
    const planBenefits = await this.entityManager.find(HmoPlanBenefitModel, {
      where: {
        id: In(benefitIds),
        planTypeId,
      },
    });
    const [planBenefitsToDelete, planBenefitsToEdit] = planBenefits.reduce(
      (acc, curr) => {
        if (benefitIds.includes(curr.id)) {
          if (
            curr.utilisationTypes.findIndex((u) =>
              utilizationTypeIds.includes(u.id),
            ) === -1
          ) {
            acc[0] = [...acc[0], curr];
          } else {
            acc[1] = [...acc[1], curr];
          }
        }
        return acc;
      },
      [[], []] as Array<HmoPlanBenefitModel[]>,
    );
    if (planBenefitsToDelete.length) {
      await this.entityManager.delete(
        HmoPlanBenefitModel,
        planBenefitsToDelete.map(({ id }) => id),
      );
    }
    if (planBenefitsToEdit.length) {
      planBenefitsToEdit.map(async (benefits) => {
        const utiIds = benefits.utilisationTypes.map(({ id }) => id);
        const utiIdsToDelete = utiIds.filter((id) =>
          utilizationTypeIds.includes(id),
        );
        await this.entityManager.update(HmoPlanBenefitModel, benefits.id, {
          utilisationTypes: benefits.utilisationTypes.filter(
            (u) => !utiIdsToDelete.includes(u.id),
          ),
          updatedBy: mutator,
        });
      });
    }
    return this.entityManager.findOne(HmoPlanTypeModel, {
      where: { id: planTypeId },
      relations: ['benefits', 'benefits.visitType'],
    });
  }

  async deletePlans(
    mutator: ProfileModel,
    planTypeId: string[],
  ): Promise<HmoPlanTypeModel[]> {
    const plan = await this.repository.deletePlans(mutator, planTypeId);
    return plan;
  }

  async archivePlans(
    mutator: ProfileModel,
    planTypeId: string[],
    archive: boolean,
  ): Promise<HmoPlanTypeModel[]> {
    const plan = await this.repository.archivePlans(
      mutator,
      planTypeId,
      archive,
    );
    return plan;
  }
  fetchPlanTypes(
    hospitalId: string,
    filterOptions: HmoPlanFilterOptions,
  ): Promise<HmoPlanTypesResponse> {
    return this.repository.fetchPlanTypes(hospitalId, filterOptions);
  }
  fetchPlanTypesByProviderId(
    providerId: string,
    filterOptions: HmoPlanFilterOptions,
  ): Promise<HmoPlanTypesResponse> {
    return this.repository.fetchPlanTypesByProviderId(
      providerId,
      filterOptions,
    );
  }

  getPlanTypeById(planTypeId: string): Promise<HmoPlanTypeModel> {
    return this.repository.getPlanTypeById(planTypeId);
  }

  async getAgreedTariff(mutator: ProfileModel, input: GetAgreedTariffInput) {
    return this.repository.getAgreedTariff(this.dataSource, mutator, input);
  }
  fetchPlanBeneficiariesByProviderId(
    providerId: string,
    filterOptions: HmoPlanBeneficiariesFilterOptions,
  ): Promise<HmoPlanBeneficiariesResponse> {
    return this.repository.fetchPlanBeneficiariesByProviderId(
      providerId,
      filterOptions,
    );
  }
  async addBeneficiariesToPlan(
    profile: ProfileModel,
    planId: string,
    hmoProfileId: string[],
  ) {
    const item = await customDSSerializeInTransaction(
      this.dataSource,
      (manager: EntityManager) =>
        manager
          .withRepository(this.repository)
          .addBeneficiariesToPlan(profile, planId, hmoProfileId),
    );
    return item;
  }
  async removeBeneficiariesFromPlan(
    profile: ProfileModel,
    planId: string,
    hmoProfileId: string[],
    manager?: EntityManager,
  ) {
    if (manager) {
      return manager
        .withRepository(this.repository)
        .removeBeneficiariesFromPlan(profile, planId, hmoProfileId);
    }
    return customDSSerializeInTransaction(
      this.dataSource,
      (manager: EntityManager) =>
        manager
          .withRepository(this.repository)
          .removeBeneficiariesFromPlan(profile, planId, hmoProfileId),
    );
  }
  fetchHmoPlansByProviderId(
    providerId: string,
    memberNumber: string,
  ): Promise<HmoPlanTypeModel[]> {
    return this.repository.fetchHmoPlansByProviderId(providerId, memberNumber);
  }
  addHmoProfilePlanByCoverageInformation(
    mutator: ProfileModel,
    input: CoverageInformationModel,
    planStatus: HmoPlanStatus,
    manager?: EntityManager,
  ) {
    const repo = manager
      ? manager.withRepository(this.repository)
      : this.repository;
    return repo.addHmoProfilePlanByCoverageInformation(
      mutator,
      input,
      planStatus,
    );
  }

  updateHmoProfilePlanByCoverageInformation(
    mutator: ProfileModel,
    updatedRecord: CoverageInformationModel,
    oldRecord: CoverageInformationModel,
    manager?: EntityManager,
  ) {
    const repo = manager
      ? manager.withRepository(this.repository)
      : this.repository;
    return repo.updateHmoProfilePlanByCoverageInformation(
      mutator,
      updatedRecord,
      oldRecord,
    );
  }

  removeHmoProfilePlanByCoverageInformation(
    mutator: ProfileModel,
    input: CoverageInformationModel,
    manager?: EntityManager,
  ) {
    const repo = manager
      ? manager.withRepository(this.repository)
      : this.repository;
    return repo.removeHmoProfilePlanByCoverageInformation(mutator, input);
  }

  async requestPreAuthorization(
    mutator: ProfileModel,
    input: PreauthorisationInput,
    manager?: EntityManager,
  ) {
    const repo = manager
      ? manager.withRepository(this.repository)
      : this.repository;
    const result = await repo.requestPreAuthorization(mutator, input);
    const hmoProfile = await this.entityManager.findOne(HmoProfileModel, {
      where: {
        memberNumber: input.enrolleeId,
        providerId: input.providerId,
      },
      relations: ['provider'],
    });
    if (!hmoProfile) {
      throw new BadRequestException('HMO Profile Not Found');
    }

    const hmoPlanType = await this.entityManager.findOne(HmoPlanTypeModel, {
      where: {
        id: hmoProfile.memberPlanId,
      },
    });
    if (!hmoPlanType) {
      throw new BadRequestException('HMO Plan Type Not Found');
    }

    const businessRuleResult =
      await this.businessRuleService.evaluateBusinessRuleWithFiltering(
        hmoPlanType.hospitalId,
        {
          hmoProviderId: input.providerId,
          planId: hmoProfile.memberPlanId,
          enrolleeId: input.enrolleeId,
          diagnosis: input.diagnosis
            .map((d) =>
              [d.diagnosisICD10, d.diagnosisICD11, d.diagnosisSNOMED].filter(
                Boolean,
              ),
            )
            .flat(),
          visitationType: input.serviceType,
        },
        result.utilizations.map((util) => ({
          utilizationCategory: util.category,
          utilizationType: util.type,
          quantity: Number(util.quantity),
          utilizationId: util.utilizationCode,
          amount: util.price
            ? parseFloat(util.price) * Number(util.quantity)
            : 0,
          enrolleeStatus: hmoProfile.memberStatus,
          planCategory: hmoProfile.planCategory,
          specialty: util.specialty,
        })),
      );

    if (businessRuleResult?.flags?.length) {
      // Always set flags on utilizations
      result.utilizations = result.utilizations.map((util) => {
        util.flags = businessRuleResult.flags
          .filter((f) => f.utilizationId === util.utilizationCode)
          .map((v) => ({ flag: v.flag, ruleId: v.ruleId } as FlagDto));
        return util;
      });
      if (businessRuleResult.shouldPreventSubmission) {
        // Filter out flagged utilizations from the result
        result.utilizations = result.utilizations.filter((util) =>
          businessRuleResult.notFlaggedUtilizations.some(
            (notFlagged) => notFlagged.utilizationId === util.utilizationCode,
          ),
        );
      }

      return result;
    }
    return result;
  }

  async updatePreAuthorization(
    mutator: ProfileModel,
    preAuthId: string,
    input: PreauthorisationUpdateInput,
    _manager?: EntityManager,
  ) {
    const repo = _manager
      ? _manager.withRepository(this.repository)
      : this.repository;
    const manager = _manager || this.entityManager;
    const pendingUtilizations = input?.utilizations?.filter((util) => !util.id);
    let businessRuleResult: {
      flags: FlagDto[];
      flaggedUtilizations: BusinessRuleActionInput[];
      notFlaggedUtilizations: BusinessRuleActionInput[];
      shouldPreventSubmission: boolean;
    };
    if (pendingUtilizations?.length) {
      const hmoProfile = await manager.findOne(HmoProfileModel, {
        where: {
          memberNumber: input.enrolleeId,
          providerId: input.providerId,
        },
        relations: ['provider'],
      });
      if (!hmoProfile) {
        throw new BadRequestException('HMO Profile Not Found');
      }
      const hmoPlanType = await manager.findOne(HmoPlanTypeModel, {
        where: {
          id: hmoProfile.memberPlanId,
        },
      });
      if (!hmoPlanType) {
        throw new BadRequestException('HMO Plan Type Not Found');
      }
      businessRuleResult =
        await this.businessRuleService.evaluateBusinessRuleWithFiltering(
          hmoPlanType.hospitalId,
          {
            hmoProviderId: input.providerId,
            planId: hmoProfile.memberPlanId,
            enrolleeId: input.enrolleeId,
            diagnosis:
              input.diagnosis
                ?.map((d) =>
                  [
                    d.diagnosisICD10,
                    d.diagnosisICD11,
                    d.diagnosisSNOMED,
                  ].filter(Boolean),
                )
                ?.flat() || [],
            visitationType: input.serviceType,
          },
          pendingUtilizations?.map((util) => ({
            utilizationCategory: util.category,
            utilizationType: util.type,
            quantity: Number(util.quantity),
            utilizationId: util.utilizationCode,
            amount: util.price
              ? parseFloat(util.price) * Number(util.quantity)
              : 0,
            enrolleeStatus: hmoProfile.memberStatus,
            planCategory: hmoProfile.planCategory,
            specialty: util.specialty,
          })),
          _manager,
        );
    }
    const result = await repo.updatePreAuthorization(mutator, preAuthId, input);
    if (businessRuleResult?.flags?.length) {
      // Always set flags on utilizations
      result.utilizations = result.utilizations.map((util) => {
        util.flags = businessRuleResult.flags
          .filter((f) => f.utilizationId === util.utilizationCode)
          .map((v) => ({ flag: v.flag, ruleId: v.ruleId } as FlagDto));
        return util;
      });
      if (businessRuleResult.shouldPreventSubmission) {
        // Filter out flagged utilizations from the result
        result.utilizations = result.utilizations.filter((util) =>
          businessRuleResult.notFlaggedUtilizations.some(
            (notFlagged) => notFlagged.utilizationId === util.utilizationCode,
          ),
        );
      }

      return result;
    }
    return result;
  }
  async updatePreAuthUtilizationStatus(
    mutator: ProfileModel,
    input: UpdatePreAuthUtilizationStatusInput,
  ): Promise<PreAuthUtilisationsModel> {
    const util = await this.repository.updatePreAuthUtilizationStatus(
      mutator,
      input,
    );
    this.autoProcessHmoClaimUtilizations(
      mutator.hmoId,
      util.hmoClaimId,
      mutator.type as UserType,
    );
    this.autoDrugPreauthUtilizationProcess(mutator, util.preAuthorizationId);
    return util;
  }

  async submitClaim(
    mutator: ProfileModel,
    input: NewHmoClaimInput,
    manager?: EntityManager,
    ignoreBusinessRule = false,
  ) {
    const repo = manager
      ? manager.withRepository(this.repository)
      : this.repository;
    const result = await repo.submitClaim(mutator, input);
    const hmoProfile = await this.entityManager.findOne(HmoProfileModel, {
      where: {
        memberNumber: input.enrolleeId,
        providerId: input.providerId,
      },
      relations: ['provider'],
    });
    if (!hmoProfile) {
      throw new BadRequestException('HMO Profile Not Found');
    }
    const hmoPlanType = await this.entityManager.findOne(HmoPlanTypeModel, {
      where: {
        id: input.memberPlanId,
      },
    });
    if (!hmoPlanType) {
      throw new BadRequestException('HMO Plan Type Not Found');
    }
    if (!ignoreBusinessRule) {
      const businessRuleResult =
        await this.businessRuleService.evaluateBusinessRuleWithFiltering(
          hmoPlanType.hospitalId,
          {
            hmoProviderId: input.providerId,
            planId: input.memberPlanId,
            enrolleeId: input.enrolleeId,
            diagnosis: input.diagnosis
              .map((d) =>
                [d.diagnosisICD10, d.diagnosisICD11, d.diagnosisSNOMED].filter(
                  Boolean,
                ),
              )
              .flat(),
            visitationType: input.serviceType,
          },
          result.utilizations.map((util) => ({
            utilizationCategory: util.category,
            utilizationType: util.type,
            quantity: Number(util.quantity),
            utilizationId: util.utilizationCode,
            amount: util.price
              ? parseFloat(util.price) * Number(util.quantity)
              : 0,
            enrolleeStatus: hmoProfile.memberStatus,
            planCategory: hmoProfile.planCategory,
            specialty: util.specialty,
          })),
        );
      if (businessRuleResult?.flags?.length) {
        result.utilizations = result.utilizations.map((util) => {
          util.flags = businessRuleResult.flags
            .filter((f) => f.utilizationId === util.utilizationCode)
            .map(
              (v) =>
                ({
                  flag: v.flag,
                  ruleId: v.ruleId,
                } as FlagDto),
            );
          return util;
        });
        if (businessRuleResult.shouldPreventSubmission) {
          // Filter out flagged utilizations from the result
          result.utilizations = result.utilizations.filter((util) =>
            businessRuleResult.notFlaggedUtilizations.some(
              (notFlagged) => notFlagged.utilizationId === util.utilizationCode,
            ),
          );
        }
        return result;
      }
      return result;
    }
    return result;
  }
  async submitCapitationClaim(
    mutator: ProfileModel,
    input: NewHmoClaimInput,
    utilisations: PreauthUtilisationInput[],
    origin?: string,
  ) {
    const result = await this.repository.submitCapitationClaim(
      mutator,
      input,
      utilisations,
    );

    const details = {
      modelName: DashboardIcon.Claim,
      action: NotificationTag.Submitted,
      item: result,
    };
    const availableVettingGroups = await this.getHmoProviderRoles(
      result.providerId || result.provider?.id || mutator.hmoId,
    );

    await this.pubSub.publish(HMOClaimAdded, {
      [HMOClaimAdded]: {
        ...result,
        __availableVettingGroups: availableVettingGroups,
      },
    });
    if (origin) {
      const provider = await this.entityManager.findOne(HmoProviderModel, {
        where: { id: result.providerId },
        relations: { hospital: true },
      });
      this.mailDocService.sendEnrolleeHmoClaimEmail(
        [{ ...result, provider }],
        origin,
      );
    }
    this.notificationService.handleNoticationEvent({
      profile: mutator,
      details,
    });
    return result;
  }
  async movePreAuthUtilizationToClaim(
    mutator: ProfileModel,
    hmoProfile: HmoProfileModel,
    input: PreauthorisationModel,
  ) {
    return this.repository.movePreAuthUtilizationToClaim(
      mutator,
      hmoProfile,
      input,
      this.businessRuleService.evaluateBusinessRule.bind(
        this.businessRuleService,
      ),
    );
  }

  private sendClaimPaymentAdviceToProviderSupportMail(
    hmoClaims: HmoClaimModel[],
    hmoProvider: HmoProviderModel,
    origin?: string,
  ) {
    try {
      const groupedByProviders = groupBy(hmoClaims, 'hospitalId');
      const providerIds = Object.keys(groupedByProviders);

      for (const providerId of providerIds) {
        const claims = groupedByProviders[providerId];
        this.mailDocService.sendClaimPaymentAdviceEmail(
          claims,
          hmoProvider,
          undefined,
          origin,
          claims.length === 1 ? claims[0].transferFunds?.[0] : undefined,
        );
      }
    } catch {
      // no-op
    }
  }

  async updateClaimStatus(
    mutator: ProfileModel,
    status: string,
    claimId: string,
  ): Promise<HmoClaimModel> {
    if (['Sent Back', 'Returned'].includes(status)) {
      return this.repository.updateClaimRevertAction(
        this.dataSource,
        mutator,
        status,
        claimId,
      );
    }

    const updatedClaim = await this.repository.updateClaimStatus(
      mutator,
      status,
      claimId,
    );

    const hmoProvider = await this.repository.findOne({
      where: {
        id: updatedClaim.providerId,
      },
      relations: {
        hospital: true,
      },
    });

    this.sendClaimPaymentAdviceToProviderSupportMail(
      [updatedClaim],
      hmoProvider,
    );

    return updatedClaim;
  }
  async bulkUpdateClaimStatus(
    mutator: ProfileModel,
    status: string,
    claimIds: string[],
  ): Promise<HmoClaimModel[]> {
    return customDSSerializeInTransaction(this.dataSource, async (manager) => {
      const trnxRepo = manager.withRepository(this.repository);
      const updatedClaims = await Promise.all(
        claimIds.map((claimId) =>
          trnxRepo.updateClaimStatus(mutator, status, claimId),
        ),
      );

      const hmoProvider = await this.repository.findOne({
        where: { id: updatedClaims[0].providerId },
        relations: { hospital: true },
      });

      await Promise.all(
        updatedClaims.map((updatedClaim) =>
          this.sendClaimPaymentAdviceToProviderSupportMail(
            [updatedClaim],
            hmoProvider,
          ),
        ),
      );

      return updatedClaims;
    });
  }
  async updateClaimUtilizationsStatus(
    mutator: ProfileModel,
    input: UpdateUtilizationsStatusInput,
  ): Promise<HmoClaimModel[]> {
    const claims = await this.repository.updateClaimUtilizationsStatus(
      mutator,
      input,
    );
    Promise.all(
      claims.map((claim) => {
        this.autoProcessHmoClaimUtilizations(
          claim.providerId,
          claim.id,
          mutator.type as UserType,
        );
      }),
    );

    return claims;
  }

  async autoProcessHmoClaimUtilizations(
    hmoId: string,
    hmoClaimId: string,
    lastApprovalGroup?: UserType | null,
    isFirstStage = false,
  ) {
    try {
      return await customDSSerializeInTransaction(
        this.dataSource,
        async (manager: EntityManager) => {
          const hospital = await manager.findOne(HospitalModel, {
            where: { hmoId },
            select: ['id'],
          });
          if (!hospital) return;
          const facilityPreference = await manager.findOne(
            FacilityPreferenceModel,
            {
              where: { hospitalId: hospital.id },
              select: ['id', 'autoProcessClaims'],
            },
          );
          const agencyEnabledAutoProcessing =
            facilityPreference?.autoProcessClaims === true;
          const agencyDoesNotAllowAutoProcessing =
            facilityPreference?.autoProcessClaims === false;
          if (agencyDoesNotAllowAutoProcessing) return;
          const hmoClaim = await manager.findOne(HmoClaimModel, {
            where: { id: hmoClaimId },
            relations: ['utilizations'],
          });
          const roles = await this.getHmoProviderRoles(hmoId);

          let currentLastApprovalGroup = lastApprovalGroup;
          let [orderedRoles, roleToAutoProcessFor] = getOrderedItemsAndNextItem(
            hmoAgencyUserProcessOrder,
            roles,
            currentLastApprovalGroup,
          );

          while (roleToAutoProcessFor) {
            const _profiles = await manager.find(ProfileModel, {
              relations: {
                profilePreference: true,
              },
              where: {
                hmoId,
                type: In([roleToAutoProcessFor]),
                profilePreference: {
                  autoProcessClaims: agencyEnabledAutoProcessing
                    ? (Not(false) as any)
                    : true,
                },
              },
            });

            _profiles.forEach((p) => {
              hmoClaim.utilizations = hmoRoleUtilizationValidation(
                hmoClaim.utilizations,
                p,
                roles,
              );
              hmoClaim.utilizations.forEach((u) => {
                if (!u.aiReason) return;
                if (
                  u.utilisationStatus?.some(
                    ({ creatorId, vettingGroup }) =>
                      creatorId === p.id ||
                      vettingGroup === currentLastApprovalGroup,
                  ) &&
                  !isFirstStage
                )
                  return;
                if (!u.utilisationStatus) {
                  u.utilisationStatus = [];
                }
                u.utilisationStatus.push({
                  creatorId: p.id,
                  creatorName: p.fullName,
                  isAutoProcessed: true,
                  status: u.aiReason.status,
                  createdDate: new Date(),
                  vettingGroup: p.type,
                });
                const newUtilizationStatus = this.repository.generateStatus(
                  u,
                  roles,
                  !!u.preAuthorizationId,
                );
                u.status = newUtilizationStatus || u.status;
              });
            });

            await manager.save(PreAuthUtilisationsModel, hmoClaim.utilizations);
            let newClaimStatus;
            if (
              hmoClaim?.status &&
              ['paid', 'partially paid'].includes(hmoClaim.status.toLowerCase())
            ) {
              newClaimStatus = hmoClaim.status;
            } else {
              const c = await manager.findOne(HmoClaimModel, {
                where: {
                  id: hmoClaim.id,
                  providerId: hmoClaim.providerId,
                },
                relations: ['utilizations', 'profile', 'provider'],
              });
              newClaimStatus = this.repository.getNewClaimStatus(
                c.utilizations,
                roles,
                `${c?.provider?.providerCode}` === '33',
              );
              if (newClaimStatus !== c.status) {
                hmoClaim.status = newClaimStatus;
                await manager.update(HmoClaimModel, c.id, {
                  status: newClaimStatus,
                });
              }
            }

            currentLastApprovalGroup = roleToAutoProcessFor;
            [orderedRoles, roleToAutoProcessFor] = getOrderedItemsAndNextItem(
              hmoAgencyUserProcessOrder,
              roles,
              currentLastApprovalGroup,
            );
          }

          return hmoClaim;
        },
      );
    } catch (error) {
      this.logger.error(
        `Error in autoProcessHmoClaimUtilizations: ${JSON.stringify(error)}`,
        'AUTO_PROCESS_HMO_CLAIM_UTILIZATIONS',
      );
    }
  }

  async autoProcessPreauthUtilizations(hmoId: string, preauthId: string) {
    try {
      return await customDSSerializeInTransaction(
        this.dataSource,
        async (manager: EntityManager) => {
          const hospital = await manager.findOne(HospitalModel, {
            where: { hmoId },
            select: ['id'],
          });
          if (!hospital) return;

          const facilityPreference = await manager.findOne(
            FacilityPreferenceModel,
            {
              where: { hospitalId: hospital.id },
              select: ['id', 'autoProcessPreauthorizations'],
            },
          );
          const agencyEnabledAutoProcessing =
            facilityPreference?.autoProcessPreauthorizations === true;
          const agencyDoesNotAllowAutoProcessing =
            facilityPreference?.autoProcessPreauthorizations === false;
          if (agencyDoesNotAllowAutoProcessing) return;

          const preauth = await manager.findOne(PreauthorisationModel, {
            where: { id: preauthId },
            relations: ['utilizations'],
          });
          if (!preauth) return;

          const roles = await this.getHmoProviderRoles(hmoId);

          // Get profiles that can auto-process preauthorizations
          const _profiles = await manager.find(ProfileModel, {
            relations: {
              profilePreference: true,
            },
            where: {
              hmoId,
              type: In(roles),
              profilePreference: {
                autoProcessPreauthorizations: agencyEnabledAutoProcessing
                  ? (Not(false) as any)
                  : true,
              },
            },
          });

          _profiles.forEach((p) => {
            preauth.utilizations = hmoRoleUtilizationValidation(
              preauth.utilizations,
              p,
              roles,
            );
            preauth.utilizations.forEach((u) => {
              if (!u.aiReason) return;
              if (
                u.utilisationStatus?.some(({ creatorId }) => creatorId === p.id)
              )
                return;
              if (!u.utilisationStatus) {
                u.utilisationStatus = [];
              }
              u.utilisationStatus.push({
                creatorId: p.id,
                creatorName: p.fullName,
                isAutoProcessed: true,
                status: u.aiReason.status,
                createdDate: new Date(),
                vettingGroup: p.type,
              });
              const newUtilizationStatus = this.repository.generateStatus(
                u,
                roles,
                !!u.preAuthorizationId,
              );
              u.status = newUtilizationStatus || u.status;
            });
          });

          await manager.save(PreAuthUtilisationsModel, preauth.utilizations);

          return preauth;
        },
      );
    } catch (error) {
      this.logger.error(
        `Error in autoProcessPreauthUtilizations: ${JSON.stringify(error)}`,
        'AUTO_PROCESS_PREAUTH_UTILIZATIONS',
      );
    }
  }

  async updatePreAuthUtilizationsStatus(
    mutator: ProfileModel,
    input: UpdateUtilizationsStatusInput,
  ) {
    return this.repository.updatePreAuthUtilizationsStatus(mutator, input);
  }

  createPreauthorizationReferralRequest(
    mutator: ProfileModel,
    input: PreauthorisationReferralInput,
  ) {
    return this.repository.createPreauthorizationReferralRequest(
      mutator,
      input,
    );
  }

  getAllUtilizationTypes(
    providerId: string,
    visitTypeId: string,
    keyword: string,
  ): Promise<UtilizationTypeObject[]> {
    return this.repository.getAllUtilizationTypes(
      this.dataSource,
      providerId,
      visitTypeId,
      keyword,
    );
  }

  findUtilizationType(
    providerId: string,
    visitTypeId: string,
    keyword: string,
  ): Promise<UtilizationTypeObject> {
    return this.repository.findUtilizationType(
      this.dataSource,
      providerId,
      visitTypeId,
      keyword,
    );
  }

  private async updateBulkUtilizationsStatusInTransaction(
    manager: EntityManager,
    mutator: ProfileModel,
    input: UpdateUtilizationsStatusInput,
  ): Promise<PreAuthUtilisationsModel[]> {
    let utils = await manager.find(PreAuthUtilisationsModel, {
      where: {
        id: In(input.ids),
      },
      relations: [
        'preAuthorization',
        'hmoClaim',
        'preAuthorization.profile',
        'preAuthorization.hospital',
      ],
      order: {
        type: 'ASC',
      },
    });
    if (!utils.length) {
      throw new NotFoundException('Utilizations Not Found');
    }
    utils = utils.filter(({ transferFundId }) => !transferFundId);
    if (!utils.length) {
      throw new NotFoundException('These Utilizations Has Been Paid For');
    }

    const facilityTypes = await manager.find(ProfileModel, {
      select: ['type'],
      where: { hospital: { id: mutator.hospitalId } },
    });
    const facilityTypeList = (facilityTypes || []).map(({ type }) => type);

    const updates = await Promise.all(
      utils.map(async (util) => {
        const vettingIndex = (util.utilisationStatus || []).findIndex(
          (i) => i.creatorId === mutator.id,
        );

        const serviceAmount =
          util.amountCovered ||
          Number(util.quantity || '0') * Number(util.price || '0');

        if (vettingIndex !== -1) {
          util.utilisationStatus[vettingIndex].status = input.status;
          util.utilisationStatus[vettingIndex].rejectionReason =
            input.rejectionReason;
          util.utilisationStatus[vettingIndex].specifyReasonForRejection =
            input?.specifyReasonForRejection;
          util.utilisationStatus[vettingIndex].statusDescription =
            input.statusDescription;
          util.utilisationStatus[vettingIndex].serviceAmount = serviceAmount;
          util.utilisationStatus[vettingIndex].updatedDate = new Date();
          util.utilisationStatus[vettingIndex].vettingGroup = mutator.type;
        } else {
          if (!util.utilisationStatus) {
            util.utilisationStatus = [];
          }
          util.utilisationStatus.push({
            status: input.status,
            rejectionReason: input.rejectionReason,
            specifyReasonForRejection: input?.specifyReasonForRejection,
            statusDescription: input.statusDescription,
            serviceAmount,
            vettingGroup: mutator.type,
            createdDate: new Date(),
            creatorId: mutator.id,
            creatorName: mutator.fullName,
          });
        }
        const newStatus = this.repository.generateStatus(
          util,
          facilityTypeList,
          !!util?.preAuthorizationId,
        );
        util.status = newStatus || input.status;
        util.rejectionReason = input.rejectionReason;
        util.specifyReasonForRejection = input?.specifyReasonForRejection;
        util.statusDescription = input.statusDescription;
        util.updatedBy = mutator;
        util.updatedDate = new Date();
        util.lastModifierName = mutator.fullName;

        await manager.save(PreAuthUtilisationsModel, util);

        if (util.hmoClaim?.providerId) {
          const claim = await manager.findOne(HmoClaimModel, {
            where: {
              id: util.hmoClaim.id,
              providerId: util.hmoClaim.providerId,
            },
            relations: ['utilizations', 'profile', 'provider'],
          });

          let newClaimStatus;
          let newPayoutStatus;
          if (
            claim?.status &&
            ['paid', 'partially paid'].includes(claim.status.toLowerCase())
          ) {
            newClaimStatus = claim.status;
            if (
              claim.payoutStatus === 'paid' &&
              input.status === PaCodeStatus.Approved
            ) {
              newPayoutStatus = 'partially paid';
            }
          } else {
            newClaimStatus = this.repository.getNewClaimStatus(
              claim.utilizations,
              facilityTypeList,
              `${claim?.provider?.providerCode}` === '33',
            );
          }

          if (claim && newClaimStatus) {
            const hmoClaim = { ...claim };
            if (
              newClaimStatus !== claim.status ||
              claim.payoutStatus !== newPayoutStatus
            ) {
              hmoClaim.status = newClaimStatus;
              hmoClaim.payoutStatus = newPayoutStatus;
              hmoClaim.updatedBy = mutator;
              hmoClaim.lastModifierName = mutator.fullName;
              await manager.save(HmoClaimModel, hmoClaim);
            }

            util.hmoClaim = hmoClaim;
          }
        }

        if (util.preAuthorization) {
          if (util.preAuthorization.claimId) {
            const claim = await manager.findOne(HmoClaimModel, {
              where: {
                claimId: util.preAuthorization.claimId,
                providerId: util.hmoProviderId,
                profileId: util.preAuthorization.profileId,
              },
              relations: ['utilizations'],
            });
            if (claim) {
              const claimUtil = claim.utilizations.find(
                (cUtil) => cUtil.utilizationCode === util.utilizationCode,
              );
              if (claimUtil) {
                claimUtil.status = input.status;
                claimUtil.rejectionReason = input.rejectionReason;
                claimUtil.specifyReasonForRejection =
                  input?.specifyReasonForRejection;
                claimUtil.statusDescription = input.statusDescription;
                claimUtil.updatedBy = mutator;
                claimUtil.updatedDate = new Date();
                claimUtil.lastModifierName = mutator.fullName;
                await manager.save(PreAuthUtilisationsModel, claimUtil);
              }
            }
          }
        }
        if (util.preAuthorization && !util.preAuthorization.responseDateTime) {
          util.preAuthorization.responseDateTime = new Date();
          await manager.update(
            PreauthorisationModel,
            { id: util.preAuthorization.id },
            { responseDateTime: new Date() },
          );
          // await manager.save(PreauthorisationModel, util.preAuthorization);
        }
        if (util.hmoClaim) {
          util.hmoClaim = await manager.findOne(HmoClaimModel, {
            where: {
              id: util.hmoClaim.id,
            },
            relations: ['utilizations', 'profile', 'hospital'],
            order: {
              utilizations: {
                type: 'ASC',
              },
            },
          });
        }
        if (util.preAuthorization) {
          util.preAuthorization = await manager.findOne(PreauthorisationModel, {
            where: {
              id: util.preAuthorization.id,
            },
            relations: ['utilizations', 'profile', 'hospital'],
            order: {
              utilizations: {
                type: 'ASC',
              },
            },
          });
        }

        return util;
      }),
    );

    // Apply auto process logic where applicable
    updates
      .map(({ hmoClaimId }) => hmoClaimId)
      ?.filter(Boolean)
      .forEach((_hmoClaimId) => {
        this.autoProcessHmoClaimUtilizations(
          mutator.hmoId,
          _hmoClaimId,
          mutator.type as UserType,
        );
      });
    const uniquePreAuthIds = [
      ...new Set(updates.map((u) => u.preAuthorizationId).filter(Boolean)),
    ];
    uniquePreAuthIds.forEach((preAuthId) => {
      this.autoDrugPreauthUtilizationProcess(mutator, preAuthId);
    });

    return updates;
  }

  async updateBulkUtilizationsStatus(
    mutator: ProfileModel,
    input: UpdateUtilizationsStatusInput,
  ): Promise<PreAuthUtilisationsModel[]> {
    return customDSSerializeInTransaction(
      this.dataSource,
      (manager: EntityManager) =>
        this.updateBulkUtilizationsStatusInTransaction(manager, mutator, input),
    );
  }

  async sendHmoClaimsPaymentAdviceToProvider(
    mutator: ProfileModel,
    filterOptions: HmoClaimFilterInput,
    origin: string,
    hmoClaimIds?: string[],
  ): Promise<boolean> {
    if (!mutator.hmoId && !filterOptions.providerId) return;
    filterOptions.status = 'Paid';
    const hmoProvider = await this.repository.findOne({
      where: {
        id: mutator.hmoId || filterOptions.providerId,
      },
      relations: {
        hospital: true,
      },
    });
    const claims = hmoClaimIds?.length
      ? await this.hmoClaimRepo.find({
          where: { id: In(hmoClaimIds) },
          relations: [
            'profile',
            'utilizations.transferFund',
            'utilizations.transferFund.createdBy',
            'createdBy',
            'provider',
            'hospital',
          ],
        })
      : (
          await this.hmoClaimRepo.findByHospital(
            this.dataSource,
            mutator.hospital.id,
            filterOptions,
            mutator,
          )
        ).list;

    this.sendClaimPaymentAdviceToProviderSupportMail(
      claims,
      hmoProvider,
      origin,
    );

    return true;
  }
  async updateHmoPharmacySupport(
    mutator: ProfileModel,
    support: boolean,
    providers: string[],
  ) {
    return this.repository.updateHmoPharmacySupport(
      this.dataSource,
      mutator.hmoId,
      support,
      providers,
    );
  }
  async getHmoPharmacySupport(mutator: ProfileModel) {
    return this.repository.getHmoPharmacySupport(mutator.hmoId);
  }

  async getBenefitPriceVisible(hospitalId: string, providerId: string) {
    return this.repository.getBenefitPriceVisible(hospitalId, providerId);
  }

  async principalMembershipNoGenerator(args: {
    providerCode?: number;
    id?: string;
    planTypeId?: string;
  }) {
    const { providerCode, id, planTypeId } = args;
    let code = providerCode;
    let planTypeCode;
    if (id && !providerCode) {
      const provider = await this.repository.findOne({
        where: { id },
        select: ['providerCode'],
      });
      code = +provider?.providerCode;
    }
    if (planTypeId) {
      const planType = await this.entityManager.findOne(HmoPlanTypeModel, {
        where: { id: planTypeId },
        select: ['planCode'],
      });
      if (planType?.isSponsor && planType?.planCode) {
        planTypeCode = planType?.planCode;
      }
    }

    return this.repository.principalMembershipNoGenerator(code, planTypeCode);
  }

  async getHospitalByProviderCode(
    providerCode: string,
    filterOptions: HmoFilterOptions,
  ) {
    return this.repository.getHospitalByProviderCode(
      this.dataSource,
      providerCode,
      filterOptions,
    );
  }

  async getHmoAgencyRoles(
    hmoId?: string,
    providerCode?: string,
  ): Promise<string[]> {
    return this.repository.getHmoAgencyRoles(
      this.dataSource,
      hmoId,
      providerCode,
    );
  }

  async getHmoHospitalById(id: string): Promise<HmoHospitalModel> {
    return this.repository.getHmoHospitalById(this.dataSource, id);
  }

  async flagHmoHospital(
    mutator: ProfileModel,
    id: string,
    flag: string,
    unset: boolean,
  ): Promise<HmoHospitalModel> {
    return this.repository.flagHmoHospital(
      this.dataSource,
      mutator,
      id,
      flag,
      unset,
    );
  }

  async getHmoHospitalByHospitalIdAndProviderId(
    hospitalId: string,
    providerId: string,
  ): Promise<HmoHospitalModel> {
    return this.repository.getHmoHospitalByHospitalIdAndProviderId(
      this.dataSource,
      hospitalId,
      providerId,
    );
  }

  async flagHmoHospitalByHospitalIdAndProviderId(
    mutator: ProfileModel,
    hospitalId: string,
    providerId: string,
    flag: string,
    unset: boolean,
  ): Promise<HmoHospitalModel> {
    return this.repository.flagHmoHospitalByHospitalIdAndProviderId(
      this.dataSource,
      mutator,
      hospitalId,
      providerId,
      flag,
      unset,
    );
  }

  async sendCapitationTransactionReceiptEmail(
    mutator: ProfileModel,
    input: EmailTransactionReceiptInput[],
    origin: string,
    copyEmail?: string,
    manager?: EntityManager,
  ): Promise<TransactionReceiptSentResponse[]> {
    manager = manager || this.entityManager;
    const ids = input.map((_item) => _item.id).filter(Boolean);

    const transferFunds = await manager.find(TransferFundModel, {
      where: { id: In(ids), hmoProviderId: mutator.hmoId },
      relations: [
        'hospital',
        'createdBy',
        'createdBy.hospital',
        'hmoProvider',
        'hmoProvider.hospital',
        'hmoPlanType',
      ],
    });
    if (!transferFunds.length) {
      throw new NotFoundException('Capitation Payment Not Found');
    }

    return await Promise.all(
      transferFunds.map(async (transferFund) => {
        const transferFundInput = input.find(
          (_item) => transferFund.id === _item.id,
        );
        const amount = transferFundInput.amount || 0;

        const transactionDate = moment(transferFund.updatedDate).format(
          'DD MMM, YYYY h:mm A',
        );
        const beneficiaryDetails = {
          bank: transferFund?.destinationBankName,
          accountName: transferFund?.destinationAccountName,
          accountNumber: transferFund?.destinationAccountNumber,
        };
        const senderDetails = {
          bank: 'WEMA BANK',
          accountName: transferFund?.originatorName || 'CLINIFY',
        };
        const referenceNumber = transferFund?.transferReference;
        const serviceChargeAmount = transferFund?.serviceChargeAmount || 0;
        const transactionRemark = transferFund.narration;
        const monthFor = moment(transferFund.createdDate).format('MMMM YYYY');

        const enrolleeCount =
          transferFund.detailsByPlanType?.reduce(
            (acc, { enrolleeCount }) => acc + enrolleeCount,
            0,
          ) || transferFund.enrolleeCount;

        await this.mailDocService.sendCapitationTransactionReceiptEmail(
          transferFund?.hmoProvider,
          transferFund?.hospital,
          transactionDate,
          amount,
          beneficiaryDetails,
          senderDetails,
          referenceNumber,
          serviceChargeAmount,
          transactionRemark,
          monthFor,
          transferFund.hmoPlanType?.name,
          enrolleeCount,
          {
            origin,
            theme: getThemeFromUrl(origin),
          },
          copyEmail,
          transferFund.hmoPlanTypeId,
          moment(transferFund.createdDate).startOf('month').toDate(),
          moment(transferFund.createdDate).endOf('month').toDate(),
          mutator,
          transferFund?.payoutCommissionPayer,
        );

        return Promise.resolve({
          email: 'email',
          sent: true,
          id: transferFund.id,
        });
      }),
    );
  }

  async getHmoCapitatedEnrolleesData(
    hmoId: string,
    hospitalId: string | null,
    hmoPlanTypeId: string | null,
    startDate: Date | null,
    endDate: Date | null,
  ): Promise<CapitatedEnrolleesListData[]> {
    // eslint-disable-next-line @typescript-eslint/quotes
    const whereConditions = [`hmo_profiles.member_status = 'Active'`];
    const parameters = [];
    let parameterCounter = 1;
    if (hmoId) {
      whereConditions.push(`hmo_profiles.provider_id = $${parameterCounter}`);
      parameters.push(hmoId);
      parameterCounter++;
    }
    if (hospitalId) {
      whereConditions.push(
        `hmo_profiles.primary_provider_id = $${parameterCounter}`,
      );
      parameters.push(hospitalId);
      parameterCounter++;
    }
    if (hmoPlanTypeId) {
      whereConditions.push(`hmo_plan_types.id = $${parameterCounter}`);
      parameters.push(hmoPlanTypeId);
      parameterCounter++;
      whereConditions.push(
        `transfer_funds.hmo_plan_type_id = $${parameterCounter}`,
      );
      parameters.push(hmoPlanTypeId);
      parameterCounter++;
      whereConditions.push(
        `(details_by_plan_type.value->>'planType' = $${parameterCounter} OR details_by_plan_type.value->>'planType' IS NULL)`,
      );
      parameters.push(hmoPlanTypeId);
      parameterCounter++;
    }
    if (startDate && endDate) {
      whereConditions.push(
        `transfer_funds.created_date BETWEEN $${parameterCounter} AND $${
          parameterCounter + 1
        }`,
      );
      parameters.push(moment(startDate).format('YYYY-MM-DD'));
      parameters.push(moment(endDate).format('YYYY-MM-DD'));
      parameterCounter += 2;
    }

    const whereClause =
      whereConditions.length > 0
        ? `WHERE ${whereConditions.join(' AND ')}`
        : '';

    const sqlString = `
    WITH enrollees AS (
        SELECT DISTINCT ON (profiles.id, hmo_profiles.primary_provider_id, hmo_profiles.provider_id)
             profiles.id AS "profileId",
             full_name AS "fullName",
             hmo_profiles.member_number AS "memberNumber",
             users.phone_number AS "phoneNumber",
             users.non_corporate_email_address AS "emailAddress",
             hmo_profiles.member_status AS "memberStatus",
             hmo_profiles.member_start_date AS "planStartDate",
             hmo_profiles.member_due_date AS "planEndDate",
             profiles.gender AS "gender",
             details.date_of_birth AS "dateOfBirth",
             hmo_profiles.primary_provider_id AS "primaryProviderId",
             hmo_profiles.provider_id AS "hmoProviderId",
             hmo_plan_types.name AS "planName",
             hmo_plan_types.id AS "planId",
             COALESCE(
               (SELECT SUM((details_by_plan_type_elem.value->>'enrolleeCount')::integer)
                FROM jsonb_array_elements(transfer_funds.details_by_plan_type) AS details_by_plan_type_elem(value)
                WHERE (details_by_plan_type_elem.value->>'enrolleeCount') IS NOT NULL),
               transfer_funds.enrollee_count
             ) AS "enrolleeCount",
             transfer_funds.amount AS "amount",
             transfer_funds.service_charge_amount AS "serviceChargeAmount",
             transfer_funds.payout_commission_payer AS "payoutCommissionPayer",
             transfer_funds.narration AS "narration",
             hmo_profiles.primary_provider_name AS "primaryProviderName",
             hmo_profiles.primary_provider_address AS "primaryProviderAddress"
        FROM profiles
            INNER JOIN details ON details.id = profiles.details
            INNER JOIN hmo_profiles ON hmo_profiles.profile_id = profiles.id
            LEFT JOIN hmo_plan_types ON hmo_plan_types.id::text = hmo_profiles.member_plan_id::text
            LEFT JOIN transfer_funds ON hmo_profiles.primary_provider_id = transfer_funds.hospital_id
                                            AND hmo_profiles.provider_id = transfer_funds.hmo_provider_id
                                            AND transfer_funds.transfer_status = 'Success'
            LEFT JOIN LATERAL jsonb_array_elements(transfer_funds.details_by_plan_type) AS details_by_plan_type(value) ON true
            LEFT JOIN users ON users.id = profiles."user"
        ${whereClause}
    )
    SELECT "primaryProviderId",
           json_agg(
              json_build_object(
                  'profileId', enrollees."profileId",
                  'fullName', enrollees."fullName",
                  'memberNumber', enrollees."memberNumber",
                  'phoneNumber', enrollees."phoneNumber",
                  'emailAddress', enrollees."emailAddress",
                  'memberStatus', enrollees."memberStatus",
                  'planStartDate', enrollees."planStartDate",
                  'planEndDate', enrollees."planEndDate",
                  'planName', enrollees."planName",
                  'gender', enrollees.gender,
                  'dateOfBirth', enrollees."dateOfBirth",
                  'primaryProviderId', enrollees."primaryProviderId",
                  'primaryProviderName', enrollees."primaryProviderName",
                  'primaryProviderAddress', enrollees."primaryProviderAddress",
                  'narration', enrollees."narration",
                  'enrolleeCount', enrollees."enrolleeCount",
                  'amount', enrollees."amount",
                  'serviceChargeAmount', enrollees."serviceChargeAmount"
              )
           )
    FROM enrollees
    GROUP BY "primaryProviderId"
    `;

    const res = await queryDSWithSlave(this.dataSource, sqlString, parameters);

    return res.map(
      ({ primaryProviderId, json_agg }) =>
        new CapitatedEnrolleesListData(primaryProviderId, json_agg),
    );
  }

  async generateCapitationPaymentAdviceDoc(
    hmoId: string,
    hospitalId: string | null,
    hmoPlanTypeId: string | null,
    startDate: Date | null,
    endDate: Date | null,
    theme: ITemplateTheme,
    mutator?: ProfileModel,
    transactionRemark?: string,
  ) {
    const hmoProvider = await this.repository.findOne({
      where: { id: hmoId },
      relations: ['hospital'],
    });
    const data = await this.getHmoCapitatedEnrolleesData(
      hmoId,
      hospitalId,
      hmoPlanTypeId,
      startDate,
      endDate,
    );
    const bulkItems = [];
    for (const providerData of data) {
      const amountPaid = convertToNumber(
        providerData.list?.find(({ enrolleeCount }) => !!enrolleeCount)?.amount,
      );
      const totalEnrolleeCount =
        providerData?.list?.[0]?.enrolleeCount ??
        convertToNumber(providerData?.list?.length);
      const facilityName = providerData?.list?.[0]?.primaryProviderName;
      const facilityId = providerData?.list?.[0]?.primaryProviderId;
      const facilityAddress = providerData?.list?.[0]?.primaryProviderAddress;
      const serviceChargeAmount = providerData?.list?.[0]?.serviceChargeAmount;
      const commissionPayer = providerData?.list?.[0]?.payoutCommissionPayer;

      if (totalEnrolleeCount === 0 || amountPaid === 0) continue;

      let enrolleesData;
      try {
        enrolleesData = providerData?.list || [];

        bulkItems.push({
          enrollees: enrolleesData || [],
          amountPaid,
          enrolleeCount: convertToNumber(totalEnrolleeCount),
          facilityName,
          facilityId,
          facilityAddress,
          serviceChargeAmount,
          commissionPayer,
        });
      } catch (error) {
        // no-op
      }
    }

    if (bulkItems.length > 0) {
      const docDef = await bulkPrintCapitationPaymentAdvice(
        bulkItems,
        theme,
        {
          name: hmoProvider.name,
          address: hmoProvider.hospital.address,
          logo: theme.defaultLogo,
          website: hmoProvider.hospital.website,
          userName: mutator?.fullName,
        },
        transactionRemark,
      );

      return docDef;
    }
  }

  async getHmoCapitatedPaymentSummary(
    hmoFacilityId: string,
    hmoId: string,
    hmoPlanTypeId: string | null,
    startDate: Date | null,
    endDate: Date | null,
  ): Promise<HmoCapitatedPaymentSummary[]> {
    const fp = await this.entityManager.findOne(FacilityPreferenceModel, {
      where: { hospitalId: hmoFacilityId },
      select: [
        'enrolleeCapitionAmountByPlanType',
        'enrolleeCapitationAmountPerPlan',
        'enrolleeCapitationAmount',
      ],
    });
    const runQuery = async (
      hmoPlanTypeId: string | null,
      capitationAmountPerEnrollee: number,
      isPerPlanType = false,
    ) => {
      const parameters = [];
      let hmoIdPosition;
      let startDatePosition;
      let endDatePosition;
      let parameterCounter = 1;
      if (hmoId) {
        hmoIdPosition = `$${parameterCounter}`;
        parameters.push(hmoId);
        parameterCounter++;
      }
      if (startDate && endDate) {
        startDatePosition = `$${parameterCounter}`;
        parameters.push(moment(startDate).format('YYYY-MM-DD'));
        parameterCounter++;
        endDatePosition = `$${parameterCounter}`;
        parameters.push(moment(endDate).format('YYYY-MM-DD'));
        parameterCounter++;
      }

      // hmo plan type
      const hmoPlanTypeIdPosition = `$${parameterCounter}`;
      parameters.push(hmoPlanTypeId || null);
      parameterCounter++;

      const sqlString = `
      WITH
    enrollee_count AS (
      SELECT
        hp.primary_provider_id as hospital_id,
        COUNT(DISTINCT hp.id) as enrollee_count
      FROM
        hmo_profiles hp
      WHERE
        hp.provider_id = ${hmoIdPosition}
        AND hp.primary_provider_id IS NOT NULL
        AND hp.member_status = 'Active'
        AND (${hmoPlanTypeIdPosition}::uuid IS NULL OR hp.member_plan_id::uuid = ${hmoPlanTypeIdPosition})
      GROUP BY
        hp.primary_provider_id
    ),
    tranfer_fund_count AS (
      SELECT
        tf.hospital_id,
        ${
          isPerPlanType
            ? `
        COALESCE(
          (details_by_plan_type.value->>'enrolleeCount')::integer,
          tf.enrollee_count,
          0
        ) as enrollee_count
      FROM
        transfer_funds tf
      LEFT JOIN LATERAL jsonb_array_elements(tf.details_by_plan_type) AS details_by_plan_type(value) ON true
      WHERE
        tf.hmo_provider_id = ${hmoIdPosition}
        AND tf.created_date BETWEEN ${startDatePosition} AND ${endDatePosition}
        AND (
          (tf.details_by_plan_type IS NOT NULL AND (details_by_plan_type.value->>'planType')::uuid = ${hmoPlanTypeIdPosition})
          OR (tf.details_by_plan_type IS NULL AND tf.enrollee_count IS NOT NULL)
        )
        AND (
          ${hmoPlanTypeIdPosition}::uuid IS NULL 
          OR (details_by_plan_type.value->>'planType')::uuid = ${hmoPlanTypeIdPosition} 
          OR tf.hmo_plan_type_id::uuid = ${hmoPlanTypeIdPosition}
        )
        `
            : `
        COALESCE(tf.enrollee_count, 0) as enrollee_count
      FROM
        transfer_funds tf
      WHERE
        tf.hmo_provider_id = ${hmoIdPosition}
        AND tf.created_date BETWEEN ${startDatePosition} AND ${endDatePosition}
        AND tf.enrollee_count IS NOT NULL
        AND (${hmoPlanTypeIdPosition}::uuid IS NULL OR tf.hmo_plan_type_id::uuid = ${hmoPlanTypeIdPosition})
        `
        }
    )
  SELECT
    SUM(
      COALESCE(
        COALESCE(tfc.enrollee_count, ec.enrollee_count),
        0
      )
    ) AS "enrolleeCount",
    SUM(
      COALESCE(
        COALESCE(tfc.enrollee_count, ec.enrollee_count) * '${capitationAmountPerEnrollee}'::numeric,
        0
      )
    ) AS "totalCapitationAmount"
  FROM
    hospitals h
    INNER JOIN hmo_hospitals hh ON h.id = hh.hospital_id
    LEFT JOIN enrollee_count ec ON ec.hospital_id = h.id
    LEFT JOIN tranfer_fund_count tfc ON tfc.hospital_id = h.id
  WHERE
    hh.provider_id = ${hmoIdPosition}
      `;

      const res = await queryDSWithSlave(
        this.dataSource,
        sqlString,
        parameters,
      );
      return {
        totalCapitationAmount: res[0].totalCapitationAmount,
        totalEnrollees: res[0].enrolleeCount,
        enrolleeCapitationAmount: capitationAmountPerEnrollee,
      };
    };

    if (fp.enrolleeCapitationAmountPerPlan) {
      const summaries: HmoCapitatedPaymentSummary[] = [];
      for (const planType of fp.enrolleeCapitionAmountByPlanType) {
        if (hmoPlanTypeId && planType.planTypeId !== hmoPlanTypeId) {
          continue;
        }
        const res = await runQuery(planType.planTypeId, planType.amount, true);
        summaries.push({
          totalCapitationAmount: res.totalCapitationAmount,
          totalEnrollees: res.totalEnrollees,
          enrolleeCapitationAmount: planType.amount,
          planTypeName: planType.planTypeName,
        });
      }
      return summaries;
    }
    if (!fp.enrolleeCapitationAmount) {
      return [
        {
          totalCapitationAmount: null,
          totalEnrollees: null,
          enrolleeCapitationAmount: null,
        },
      ];
    }
    const res = await runQuery(
      hmoPlanTypeId,
      fp.enrolleeCapitationAmount,
      false,
    );
    return [
      {
        totalCapitationAmount: res.totalCapitationAmount,
        totalEnrollees: res.totalEnrollees,
        enrolleeCapitationAmount: fp.enrolleeCapitationAmount,
      },
    ];
  }

  async findUtilizationTypeByVisitType(
    providerId: string,
    planTypeId: string,
    visitTypeId: string,
    codes: string[],
  ): Promise<UtilizationTypeObject[]> {
    return this.repository.findUtilizationTypeByVisitType(
      this.dataSource,
      providerId,
      planTypeId,
      visitTypeId,
      codes,
    );
  }

  async getExternalPlanType(
    providerId: string,
    fetchBenefits?: boolean,
  ): Promise<HmoPlanTypeModel> {
    return this.repository.getExternalPlanType(providerId, fetchBenefits);
  }

  async getCustomPlanTypePriceByHospitalId(
    hmoProviderId: string,
    hospitalId: string,
    planTypeId: string,
  ): Promise<BenefitCustomPrice[]> {
    return this.repository.getCustomPlanTypePriceByHospitalId(
      this.dataSource,
      hmoProviderId,
      hospitalId,
      planTypeId,
    );
  }

  async updateCustomPlanTypePriceByHospitalId(
    hmoProviderId: string,
    hospitalId: string,
    planTypeId: string,
    prices: UpdateBenefitCustomPriceInput[],
  ): Promise<BenefitCustomPrice[]> {
    return this.repository.updateCustomPlanTypePriceByHospitalId(
      hmoProviderId,
      hospitalId,
      planTypeId,
      prices,
    );
  }

  async generateUniqueVerificationCode(
    manager: EntityManager,
  ): Promise<string> {
    let code: string;
    do {
      code = Array.from(
        { length: 6 },
        () =>
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'[
            Math.floor(Math.random() * 36)
          ],
      ).join('');
    } while (
      await manager.findOne(MedicationModel, {
        where: { verificationCode: code },
      })
    );
    return code;
  }

  async autoDrugPreauthUtilizationProcess(
    mutator: ProfileModel,
    preAuthId?: string,
  ): Promise<void> {
    if (!preAuthId) return;
    const { updated, created, deleted } = await customDSSerializeInTransaction(
      this.dataSource,
      async (manager: EntityManager) => {
        const provider = await manager.findOne(HmoProviderModel, {
          where: { id: mutator.hmoId },
        });
        if (!provider) {
          this.logger.warn(
            `HMO Provider with ID ${mutator.hmoId} not found`,
            'AUTO_DRUG_PREAUTH_UTILIZATION_PROCESS',
          );
          return { updated: null, created: null, deleted: null };
        }
        if (!['20', '107'].includes(provider.providerCode)) {
          this.logger.warn(
            `Feature not enabled for HMO Provider with ID ${mutator.hmoId} has provider code ${provider.providerCode}`,
            'AUTO_DRUG_PREAUTH_UTILIZATION_PROCESS',
          );
          return { updated: null, created: null, deleted: null };
        }
        const preAuth = await manager.findOne(PreauthorisationModel, {
          where: { id: preAuthId },
          relations: ['utilizations', 'profile', 'hospital', 'createdBy'],
        });
        if (!preAuth || !preAuth.utilizations?.length)
          return { updated: null, created: null, deleted: null };

        const hospital = preAuth.hospital;
        const createdBy = preAuth.createdBy;
        createdBy.hospital = hospital;

        const drugUtilizations = preAuth.utilizations.filter(
          (util) =>
            util.category.toLowerCase().startsWith('drug') &&
            util.status === 'Approved',
        );

        // Check if medication already exists for this preauth
        const existingMedication = await manager.findOne(MedicationModel, {
          where: { preauthorisationId: preAuthId },
          relations: [
            'details',
            'details.preauthorizationDetails',
            'dispenseDetails',
            'createdBy',
            'hospital',
          ],
        });
        if (existingMedication) {
          const rejectedDrugUtilizations = preAuth.utilizations.filter(
            (util) =>
              util.category.toLowerCase().startsWith('drug') &&
              util.status === 'Rejected',
          );
          const newUtilizations = drugUtilizations?.filter(
            (util) =>
              !existingMedication.details?.some(
                (detail) =>
                  detail.medicationName === util.type &&
                  detail.preauthorizationDetails,
              ),
          );
          if (!newUtilizations.length && !rejectedDrugUtilizations.length) {
            this.logger.warn(
              `No new drug utilizations to process for preauth ${preAuthId}`,
              'AUTO_DRUG_PREAUTH_UTILIZATION_PROCESS',
            );
            return { updated: null, created: null, deleted: null };
          }
          const medicationDetails = newUtilizations.map((util) => {
            return new MedicationDetailsModel({
              duration: util.duration || '',
              medicationName: util.type || '',
              medicationCategory: util.medicationCategory || '',
              purpose: '',
              type: '',
              quantity: util.quantity || '',
              dosage: util.dosage || '',
              dosageUnit: util.dosageUnit || 'Tablet',
              prescriptionNote: '',
              concealPrescriptionNote: true,
              drugInventoryId: '',
              refillNumber: 0,
              frequency: util.frequency || '',
              provider: null,
              priceDetails: {
                pricePerUnit: util.price,
                patientType: 'OutPatient',
                paymentType: null,
                name: null,
                type: null,
                quantity: util.quantity || '',
              },
              inventoryClass: 'External',
              bank: 'CLINIFY',
              option: MedicationOptionType.M,
              datePrescribed: preAuth.requestDateTime || new Date(),
              diagnosis: preAuth.diagnosis,
              createdBy,
              creatorName: createdBy.fullName,
              refProfile: preAuth.profile.id,
              hospitalId: createdBy.hospitalId,
              medicationId: existingMedication.id,
            });
          });
          const details = await manager.save(
            MedicationDetailsModel,
            medicationDetails,
          );

          for (const detail of details) {
            const util = drugUtilizations.find(
              (u) => u.type === detail.medicationName,
            );
            await manager.save(PreauthorizationDetailsModel, {
              recordType: BillableRecords.Medication,
              paStatus: util.status,
              paCode: util.paCode,
              updatedBy: createdBy,
              lastModifierName: createdBy.fullName,
              hospital: createdBy.hospital,
              providerId: mutator.hmoId,
              medicationDetailsId: detail.id,
            });
          }

          if (
            rejectedDrugUtilizations.length &&
            !existingMedication.dispenseDetails?.length
          ) {
            // Update rejected utilizations to link with existing medication
            for (const util of rejectedDrugUtilizations) {
              const existingDetail = existingMedication.details.find(
                (detail) => detail.medicationName === util.type,
              );
              if (existingDetail) {
                if (existingDetail?.preauthorizationDetails?.id) {
                  await manager.delete(
                    PreauthorizationDetailsModel,
                    existingDetail.preauthorizationDetails.id,
                  );
                }
                await manager.delete(MedicationDetailsModel, existingDetail.id);
              }
            }
            // check if no details left and delete medication if empty
            const medication = await manager.findOne(MedicationModel, {
              where: { id: existingMedication.id },
              relations: [
                'details',
                'details.preauthorizationDetails',
                'createdBy',
                'hospital',
              ],
            });
            if (medication && medication.details.length === 0) {
              await manager.delete(MedicationModel, existingMedication.id);
              this.logger.warn(
                `Deleted medication ${existingMedication.id} as no details left after processing rejected utilizations`,
                'AUTO_DRUG_PREAUTH_UTILIZATION_PROCESS',
              );
              return { updated: null, created: null, deleted: medication };
            }
          }
          const medication = await manager.findOne(MedicationModel, {
            where: { id: existingMedication.id },
            relations: [
              'details',
              'details.preauthorizationDetails',
              'createdBy',
              'hospital',
            ],
          });
          return { updated: medication, created: null };
        }

        if (!drugUtilizations.length)
          return { updated: null, created: null, deleted: null };

        // Compose medication details from approved utilizations
        const medicationDetails = drugUtilizations.map((util) => {
          return new MedicationDetailsModel({
            duration: util.duration || '',
            medicationName: util.type || '',
            medicationCategory: util.medicationCategory || '',
            purpose: '',
            type: '',
            quantity: util.quantity || '',
            dosage: util.dosage || '',
            dosageUnit: util.dosageUnit || 'Tablet',
            prescriptionNote: '',
            concealPrescriptionNote: true,
            drugInventoryId: '',
            refillNumber: 0,
            frequency: util.frequency || '',
            provider: null,
            priceDetails: {
              pricePerUnit: util.price,
              patientType: 'OutPatient',
              paymentType: null,
              name: null,
              type: null,
              quantity: util.quantity || '',
            },
            inventoryClass: 'External',
            bank: 'CLINIFY',
            option: MedicationOptionType.M,
            datePrescribed: preAuth.requestDateTime || new Date(),
            diagnosis: preAuth.diagnosis,
            createdBy,
            creatorName: createdBy.fullName,
            refProfile: preAuth.profile.id,
            hospitalId: createdBy.hospitalId,
          });
        });

        // Save medication using MedicationService
        try {
          const newMedication = new MedicationModel({
            clinifyId: preAuth.profile?.clinifyId || '',
            setReminder: false,
            reminderStartDate: null,
            reminderEndDate: null,
            medicationStartTime: null,
            medicationEndTime: null,
            interval: null,
            intervalUnit: null,
            remindMe: null,
            details: medicationDetails,
            dispenseDetails: [],
            prescribedBy: preAuth.requestedBy || '',
            specialty: preAuth.specialty || '',
            rank: preAuth.rank || '',
            department: preAuth.department || '',
            hospitalName: preAuth.facilityName || '',
            hospitalAddress: preAuth.facilityAddress || '',
            documentUrl: [],
            additionalNote: preAuth.additionalNote || '',
            concealAdditionalNote: true,
            totalQuantity: null,
            totalPrice: null,
            hospital,
            createdBy,
            profile: preAuth.profile,
            creatorName: createdBy.fullName,
            verificationCode: await this.generateUniqueVerificationCode(
              manager,
            ),
            preauthorisationId: preAuthId,
          });
          const savedMedication = await manager.save(
            MedicationModel,
            newMedication,
          );

          // Link the medication to the preauthorisation
          if (savedMedication) {
            await manager.update(
              PreauthorisationModel,
              { id: preAuthId },
              {
                code: savedMedication.verificationCode,
                updatedDate: () => 'updated_date',
              },
            );
            for (const detail of savedMedication.details) {
              const util = drugUtilizations.find(
                (u) => u.type === detail.medicationName,
              );
              await manager.save(PreauthorizationDetailsModel, {
                recordType: BillableRecords.Medication,
                paStatus: util.status,
                paCode: util.paCode,
                updatedBy: createdBy,
                lastModifierName: createdBy.fullName,
                hospital: createdBy.hospital,
                providerId: mutator.hmoId,
                medicationDetailsId: detail.id,
              });
            }
          }
          this.logger.log(
            `Created medication from preauth ${preAuthId}: ${savedMedication.id}`,
            'AUTO_DRUG_PREAUTH_UTILIZATION_PROCESS',
          );
          return { updated: null, created: savedMedication, deleted: null };
        } catch (error) {
          this.logger.error(
            `Error creating medication from preauth ${preAuthId}: ${error.message}`,
            'AUTO_DRUG_PREAUTH_UTILIZATION_PROCESS',
          );
          return { updated: null, created: null, deleted: null };
        }
      },
    );
    if (created) {
      await this.pubSub.publish(MedicationEvent, {
        medication: created,
        [MedicationEvent]: created.creatorId,
      });
      await this.pubSub.publish(PrescriptionEvent, {
        medication: created,
        [PrescriptionEvent]: EventType.ADDED,
      });
      await this.pubSub.publish(MedicationAdded, {
        [MedicationAdded]: created,
      });
      const details = {
        modelName: DashboardIcon.Medication,
        action: NotificationTag.Prescribed,
        item: created,
      };
      this.notificationService.handleNoticationEvent({
        profile: created.createdBy,
        details,
      });
    }
    if (updated) {
      await this.pubSub.publish(MedicationEvent, {
        medication: updated,
        [MedicationEvent]: EventType.UPDATED,
      });
      await this.pubSub.publish(MedicationUpdated, {
        [MedicationUpdated]: updated,
      });
      const details = {
        modelName: DashboardIcon.Medication,
        action: NotificationTag.Updated,
        item: updated,
      };
      this.notificationService.handleNoticationEvent({
        profile: updated.createdBy,
        details,
      });
    }
    if (deleted) {
      await this.pubSub.publish(MedicationEvent, {
        medication: deleted,
        [MedicationEvent]: EventType.DELETED,
      });
      await this.pubSub.publish(MedicationRemoved, {
        [MedicationRemoved]: [deleted],
      });
      const details = {
        modelName: DashboardIcon.Medication,
        action: NotificationTag.Deleted,
        item: [deleted],
      };
      this.notificationService.handleNoticationEvent({
        profile: deleted.createdBy,
        details,
      });
    }
    if (updated || created) {
      const preauthId =
        updated?.preauthorisationId || created?.preauthorisationId;
      if (preauthId) {
        const preauth = await this.entityManager.findOne(
          PreauthorisationModel,
          {
            where: { id: preauthId },
            relations: { utilizations: true, profile: true },
          },
        );
        if (preauth) {
          await this.pubSub.publish(HMOPreauthorizationUpdated, {
            [HMOPreauthorizationUpdated]: preauth,
          });
        }
      }
    }
  }

  async sendCapitationPaymentWhatsAppNotification(
    transferFundId: string,
  ): Promise<boolean> {
    const transferFund = await this.entityManager.findOne(TransferFundModel, {
      where: { id: transferFundId },
      relations: {
        hospital: true,
        hmoProvider: true,
      },
    });
    const beneficiaryHospital = transferFund?.hospital;
    if (
      beneficiaryHospital?.hospitalSupportPhoneNumber?.value &&
      transferFund?.hmoProvider?.enableWhatsAppNotifications
    ) {
      this.twilioWhatsappService.sendWhatsappMessageFromTemplate(
        TEMPLATE_SIDS.capitationPaymentNotification,
        JSON.stringify({
          1: beneficiaryHospital.name,
          2: moment(transferFund.createdDate).format('MMMM YYYY'),
          3: formatMoney(toNaira(transferFund.amount)),
          4: transferFund?.hmoProvider?.name,
        }),
        beneficiaryHospital.hospitalSupportPhoneNumber.value,
      );
      return true;
    }
    return false;
  }

  async sendFeeForServicePaymentWhatsAppNotification(
    claimId: string,
  ): Promise<boolean> {
    const claim = await this.entityManager.findOne(HmoClaimModel, {
      where: { id: claimId },
      relations: {
        hospital: true,
        transferFunds: true,
        provider: true,
      },
    });
    const beneficiaryHospital = claim?.hospital;
    if (
      beneficiaryHospital?.hospitalSupportPhoneNumber?.value &&
      claim?.transferFunds?.length &&
      claim?.provider?.enableWhatsAppNotifications
    ) {
      const amountPaid = claim?.transferFunds?.reduce(
        (acc, curr) => acc + curr.amount,
        0,
      );
      this.twilioWhatsappService.sendWhatsappMessageFromTemplate(
        TEMPLATE_SIDS.feeForServicePaymentNotification,
        JSON.stringify({
          1: beneficiaryHospital.name,
          2: moment(claim.createdDate).format('MMMM YYYY'),
          3: formatMoney(toNaira(amountPaid)),
          4: claim?.provider?.name,
        }),
        beneficiaryHospital.hospitalSupportPhoneNumber.value,
      );
      return true;
    }
    return false;
  }
}
